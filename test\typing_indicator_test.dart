import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:room_eight/viewmodels/chat_bloc/chat_bloc.dart';
import 'package:room_eight/widgets/custom_widget/custom_debounce.dart';

void main() {
  group('Typing Indicator Tests', () {
    test('should create TypingSocketEvent with correct parameters', () {
      // Arrange & Act
      const event = TypingSocketEvent(
        userId: 123,
        isTyping: "1",
      );

      // Assert
      expect(event, isA<TypingSocketEvent>());
      expect(event.userId, equals(123));
      expect(event.isTyping, equals("1"));
      expect(event.props, equals([123, "1"]));
    });

    test('should create TypingSocketEvent for stop typing', () {
      // Arrange & Act
      const event = TypingSocketEvent(
        userId: 123,
        isTyping: "0",
      );

      // Assert
      expect(event, isA<TypingSocketEvent>());
      expect(event.userId, equals(123));
      expect(event.isTyping, equals("0"));
      expect(event.props, equals([123, "0"]));
    });

    test('should verify typing state management logic', () {
      // Arrange
      bool isCurrentlyTyping = false;

      // Act - Start typing
      isCurrentlyTyping = true;

      // Assert
      expect(isCurrentlyTyping, isTrue);

      // Act - Stop typing
      isCurrentlyTyping = false;

      // Assert
      expect(isCurrentlyTyping, isFalse);
    });

    test('should verify debouncer functionality', () async {
      // Arrange
      bool callbackExecuted = false;
      final debouncer = Debouncer(const Duration(milliseconds: 100));

      // Act
      debouncer.run(
        () {
          callbackExecuted = true;
        },
        () {
          // Before callback - not needed for this test
        },
      );

      // Wait for debounce duration
      await Future.delayed(const Duration(milliseconds: 150));

      // Assert
      expect(callbackExecuted, isTrue);

      // Cleanup
      debouncer.dispose();
    });

    test('should verify debouncer reset functionality', () async {
      // Arrange
      int callbackCount = 0;
      final debouncer = Debouncer(const Duration(milliseconds: 100));

      // Act - First call
      debouncer.run(
        () {
          callbackCount++;
        },
        () {
          // Before callback - not needed for this test
        },
      );

      // Act - Second call before first completes (should reset)
      await Future.delayed(const Duration(milliseconds: 50));
      debouncer.run(
        () {
          callbackCount++;
        },
        () {
          // Before callback - not needed for this test
        },
      );

      // Wait for debounce duration
      await Future.delayed(const Duration(milliseconds: 150));

      // Assert - Only one callback should have executed
      expect(callbackCount, equals(1));

      // Cleanup
      debouncer.dispose();
    });

    test('should verify typing indicator visibility logic', () {
      // Arrange
      final ValueNotifier<bool> showTypingIndicator = ValueNotifier(false);

      // Act - Show typing indicator
      showTypingIndicator.value = true;

      // Assert
      expect(showTypingIndicator.value, isTrue);

      // Act - Hide typing indicator
      showTypingIndicator.value = false;

      // Assert
      expect(showTypingIndicator.value, isFalse);

      // Cleanup
      showTypingIndicator.dispose();
    });

    test('should verify typing indicator response filtering', () {
      // Arrange - Simulate socket response
      final Map<String, dynamic> response = {
        'from': 456, // Different user
        'is_typing': 1,
      };
      const int currentUserId = 123;

      // Act - Check if should show typing indicator
      final fromUserId = response['from'] ?? 0;
      final shouldShow = fromUserId != currentUserId && 
                        (response['is_typing'] == 1 || response['is_typing'] == "1");

      // Assert
      expect(shouldShow, isTrue);
    });

    test('should not show typing indicator for own messages', () {
      // Arrange - Simulate socket response from self
      final Map<String, dynamic> response = {
        'from': 123, // Same user
        'is_typing': 1,
      };
      const int currentUserId = 123;

      // Act - Check if should show typing indicator
      final fromUserId = response['from'] ?? 0;
      final shouldShow = fromUserId != currentUserId && 
                        (response['is_typing'] == 1 || response['is_typing'] == "1");

      // Assert
      expect(shouldShow, isFalse);
    });

    test('should verify input text change triggers typing', () {
      // Arrange
      final ValueNotifier<String> inputText = ValueNotifier('');
      bool typingStarted = false;

      // Simulate typing start logic
      void startTyping() {
        typingStarted = true;
      }

      // Act - Simulate text input
      inputText.value = 'Hello';
      if (inputText.value.isNotEmpty) {
        startTyping();
      }

      // Assert
      expect(typingStarted, isTrue);
      expect(inputText.value, equals('Hello'));

      // Cleanup
      inputText.dispose();
    });

    test('should verify empty input stops typing', () {
      // Arrange
      final ValueNotifier<String> inputText = ValueNotifier('Hello');
      bool typingStopped = false;

      // Simulate typing stop logic
      void stopTyping() {
        typingStopped = true;
      }

      // Act - Simulate clearing text
      inputText.value = '';
      if (inputText.value.isEmpty) {
        stopTyping();
      }

      // Assert
      expect(typingStopped, isTrue);
      expect(inputText.value, equals(''));

      // Cleanup
      inputText.dispose();
    });
  });
}
