import 'package:room_eight/core/utils/app_exports.dart';

class ForgotPasswordScreen extends StatelessWidget {
  static Widget builder(BuildContext context) => const ForgotPasswordScreen();
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Bloc<PERSON><PERSON>er<AuthBloc, AuthState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  buildSizedBoxH(175.h),
                  _buildHeaderMessage(context),
                  buildSizedBoxH(50.h),
                  _buildForgotPasswordForm(context, state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

Widget _buildHeaderMessage(BuildContext context) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Column(
        children: [
          Text(
            "Forgot Password?",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 30.sp,
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          buildSizedBoxH(15.h),
          Text(
            "Enter your email to get the",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          Text(
            "verification code.",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
        ],
      ),
    ],
  );
}

Widget _buildForgotPasswordForm(BuildContext context, AuthState state) {
  return Form(
    key: state.forgotPasswordFormKey,
    child: Column(
      children: [
        _buildLoginEmailField(context, state),
        buildSizedBoxH(50.h),
        _buildSignInButton(context, state),
      ],
    ),
  );
}

Widget _buildLoginEmailField(BuildContext context, AuthState state) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        Lang.of(context).lbl_school_id,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontSize: 16.sp,
          color: Theme.of(context).customColors.blackColor,
        ),
      ),
      CustomTextInputField(
        context: context,
        type: InputType.email,
        hintLabel: Lang.of(context).lbl_emptyEmail,
        controller: state.forgotEmailController,
        textInputAction: TextInputAction.next,
        prefixIcon: CustomImageView(
          margin: EdgeInsets.all(12.0),
          imagePath: Assets.images.svgs.icons.icMail.path,
        ),
        validator: (value) => AppValidations.emailValidation(value, context),
        onChanged: (value) =>
            context.read<AuthBloc>().add(ForgotEmailChanged(value)),
      ),
    ],
  );
}

Widget _buildSignInButton(BuildContext context, AuthState state) {
  return CustomElevatedButton(
    isLoading: state.isForgotPassword,
    isDisabled: state.isForgotPassword,
    text: "Continue",
    buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
      color: Theme.of(context).customColors.fillColor,
      fontSize: 18.0.sp,
      fontWeight: FontWeight.w500,
    ),
    onPressed: () {
      if (state.forgotPasswordFormKey.currentState?.validate() ?? false) {
        context.read<AuthBloc>().add(ForgotPassword());
      }
    },
  );
}
