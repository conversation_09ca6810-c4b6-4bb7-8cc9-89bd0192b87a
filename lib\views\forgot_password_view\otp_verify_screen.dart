import 'package:pinput/pinput.dart';
import 'package:room_eight/core/utils/app_exports.dart';

class OtpVerifyScreen extends StatelessWidget {
  static Widget builder(BuildContext context) => const OtpVerifyScreen();
  const OtpVerifyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  buildSizedBoxH(175.h),
                  _buildHeaderMessage(context),
                  buildSizedBoxH(50.h),
                  _buildForgotPasswordForm(context, state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

Widget _buildHeaderMessage(BuildContext context) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Column(
        children: [
          Text(
            "Enter Code",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 30.sp,
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          buildSizedBoxH(15.h),
          Text(
            "Please enter verification code,thet we",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          Text(
            "have sent no your registered email.",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
        ],
      ),
    ],
  );
}

Widget _buildForgotPasswordForm(BuildContext context, AuthState state) {
  return Form(
    key: state.otpVerifyFormKey,
    child: Column(
      children: [
        _buildLoginEmailField(context, state),
        buildSizedBoxH(50.h),
        _buildSignInButton(context, state),
      ],
    ),
  );
}

Widget _buildLoginEmailField(BuildContext context, AuthState state) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Text(
      //   "otp",
      //   style: Theme.of(context).textTheme.labelLarge?.copyWith(
      //     fontSize: 16.sp,
      //     color: Theme.of(context).customColors.blackColor,
      //   ),
      // ),
      Pinput(
        controller: state.verifyOtpController,
        length: 6,
        validator: (value) => AppValidations.validateOTP(value, context),
        showCursor: true,
        onChanged: (value) =>
            context.read<AuthBloc>().add(VerifyOtpChanged(value)),
        defaultPinTheme: PinTheme(
          width: 48,
          height: 48,

          textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),

            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    ],
  );
}

Widget _buildSignInButton(BuildContext context, AuthState state) {
  return CustomElevatedButton(
    isLoading: state.isVerifyOtp,
    isDisabled: state.isVerifyOtp,
    text: "Verify",
    buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
      color: Theme.of(context).customColors.fillColor,
      fontSize: 18.0.sp,
      fontWeight: FontWeight.w500,
    ),
    onPressed: () {
      if (state.otpVerifyFormKey.currentState?.validate() ?? false) {
        context.read<AuthBloc>().add(VerifyOtp());
      }
    },
  );
}
