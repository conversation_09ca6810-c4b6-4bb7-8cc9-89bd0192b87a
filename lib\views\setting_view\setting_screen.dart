import 'package:pinput/pinput.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/views/profile_view/widgets/profile_shimmer.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/widgets/common_widget/common_shimmer.dart';

class SettingScreen extends StatelessWidget {
  static Widget builder(BuildContext context) => const SettingScreen();
  const SettingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      appBar: _buildAppbar(context),

      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              children: [
                // _buildTopSection(context),
                // buildSizedBoxH(16.h),
                _buildUserData(context, state),
                buildSizedBoxH(12.h),
                _buildsettingItem(context, state),
              ],
            ),
          );
        },
      ),
    );
  }

  RoomEightAppBar _buildAppbar(BuildContext context) {
    return RoomEightAppBar(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      showBackButton: true,
      useGradientLeading: true,
      title: Lang.of(context).lbl_setting,
      centerTitle: true,
      ontap: () {
        context.read<NavBloc>().add(const NavTabChanged(0));
      },
      // actions: [
      //   GestureDetector(
      //     onTap: () => NavigatorService.pushNamed(AppRoutes.editProfileScreen),
      //     child: Container(
      //       margin: const EdgeInsets.only(right: 16.0),
      //       height: 36,
      //       width: 36,
      //       child: CustomGradientContainer(
      //         height: 36,
      //         width: 36,
      //         topColor: Theme.of(context).customColors.fillColor!,
      //         bottomColor: Theme.of(context).customColors.fillColor!,
      //         fillColor: Theme.of(context).customColors.fillColor!,
      //         child: CustomImageView(
      //           imagePath: Assets.images.svgs.icons.icEdit.path,
      //           margin: const EdgeInsets.all(1.5),
      //         ),
      //       ),
      //     ),
      //   ),
      // ],
    );
  }

  // Widget _buildTopSection(BuildContext context) {
  //   return Padding(
  //     padding: EdgeInsets.only(
  //       left: 16.w,
  //       right: 16.w,
  //       top: 60.h,
  //       bottom: 16.h,
  //     ),
  //     child: _buildTopBar(context),
  //   );
  // }

  // Widget _buildTopBar(BuildContext context) {
  //   final customColors = Theme.of(context).customColors;

  //   return Stack(
  //     alignment: Alignment.center,
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: CustomGradientContainer(
  //           height: 36.w,
  //           width: 36.w,
  //           topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
  //           bottomColor: customColors.blackColor!.withAlpha(
  //             (0.4 * 255).toInt(),
  //           ),
  //           fillColor: customColors.fillColor!.withAlpha(75),
  //           child: CustomImageView(
  //             imagePath: Assets.images.svgs.icons.icBackArrow.path,
  //             color: customColors.blackColor,
  //           ),
  //         ),
  //       ),
  //       Text(
  //         "Setting", // Change this to your dynamic title if needed
  //         style: Theme.of(context).textTheme.titleLarge?.copyWith(
  //           color: customColors.blackColor,
  //           fontWeight: FontWeight.w500,
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildUserData(BuildContext context, ProfileState state) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildHeaderInfo(context, state),
        // Container(
        //   height: 100.h,
        //   width: 100.w,
        //   decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(50.r),
        //     color: Colors.amber,
        //   ),
        // ),
        // buildSizedBoxH(15.h),
        // Text(
        //   "Cameron Williamson",
        //   style: Theme.of(context).textTheme.bodyLarge!.copyWith(
        //     color: Theme.of(context).customColors.blackColor,
        //     fontSize: 20.sp,
        //     fontWeight: FontWeight.w600,
        //   ),
        // ),
        // Text(
        //   "<EMAIL>",
        //   style: Theme.of(context).textTheme.bodySmall!.copyWith(
        //     color: Theme.of(context).customColors.darkGreytextcolor,
        //     fontSize: 15.sp,
        //   ),
        // ),
      ],
    );
  }

  Widget _buildHeaderInfo(BuildContext context, ProfileState state) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        if (state.isGetUserProfileLoading) {
          return CommonShimmer(
            child: Column(
              children: [
                Center(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      ShimmerBox(
                        height: 130.h,
                        width: 130.h,
                        borderRadius: BorderRadius.circular(65.h),
                      ),
                      ShimmerCircle(size: 100.h),
                    ],
                  ),
                ),
                buildSizedBoxH(16.h),
                ShimmerLine(width: 150.w, height: 20.h),
                buildSizedBoxH(4.h),
                ShimmerLine(width: 200.w, height: 14.h),
              ],
            ),
          );
        }
        return Column(
          children: [
            Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  CustomImageView(
                    height: 130.h,
                    width: 130.h,
                    imagePath: Assets.images.svgs.other.icProfileFrame.path,
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: CustomImageView(
                      height: 100.h,
                      width: 100.h,
                      fit: BoxFit.cover,
                      radius: BorderRadius.circular(100.r),
                      imagePath:
                          ApiEndPoint.getImageUrl + state.profileImagePath,
                    ),
                  ),
                ],
              ),
            ),
            buildSizedBoxH(16.h),
            Text(
              state.nameController.text.isNotEmpty
                  ? state.nameController.text
                  : Lang.of(context).lbl_dash,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: Theme.of(context).customColors.blackColor,
                fontWeight: FontWeight.bold,
                fontSize: 20.sp,
              ),
            ),
            buildSizedBoxH(4.h),
            Text(
              Prefobj.preferences!
                      .get(Prefkeys.USER_MAIL_ID)
                      .toString()
                      .isNotEmpty
                  ? Prefobj.preferences?.get(Prefkeys.USER_MAIL_ID)
                  : Lang.of(context).lbl_dash,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: Theme.of(context).customColors.darkGreytextcolor,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildsettingItem(BuildContext context, ProfileState state) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icLocks.path,
            title: Lang.of(context).lbl_privacy_policy,
            onTap: () {
              // NavigatorService.pushNamed(AppRoutes.chatScreen);
            },
          ),
          buildSizedBoxH(12.h),
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icNotifications.path,
            title: Lang.of(context).lbl_notifications,
            onTap: () {},
          ),
          buildSizedBoxH(12.h),
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icTremandcondtion.path,
            title: Lang.of(context).lbl_terms_condition,
            onTap: () {},
          ),
          buildSizedBoxH(12.h),
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icBlockAccount.path,
            title: Lang.of(context).lbl_blocked_accounts,
            onTap: () {},
          ),
          buildSizedBoxH(12.h),
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icHelp.path,
            title: Lang.of(context).lbl_get_help,
            onTap: () {},
          ),
          buildSizedBoxH(12.h),
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icLogout.path,
            title: Lang.of(context).lbl_log_out,
            onTap: () {
              showDialog(
                context: context,
                builder: (context) => CustomAlertDialog(
                  title: 'Logout',
                  subtitle: 'Are you sure you want to log out?',
                  confirmButtonText: 'Logout',
                  cancelButtonText: 'Cancel',
                  isLoading: false,
                  onConfirmButtonPressed: () {
                    Prefobj.preferences?.put(Prefkeys.IS_LOGIN, false);
                    Prefobj.preferences?.put(
                      Prefkeys.IS_USER_PROFIL_CREATED,
                      false,
                    );
                    Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, '');
                    Navigator.of(context).pop();
                    context.read<NavBloc>().add(const NavTabChanged(0));
                    NavigatorService.pushNamedAndRemoveUntil(
                      AppRoutes.loginScreen,
                    );
                  },
                  onCancelButtonPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              );
            },
          ),
          buildSizedBoxH(12.h),
          buildSettingsTile(
            context: context,
            imagePath: Assets.images.svgs.icons.icHelp.path,
            title: "Delete Account",
            onTap: () {
              showDialog(
                context: context,
                builder: (context) => CustomAlertDialog(
                  title: 'Delete Account',
                  subtitle: 'Are you sure you want delete account?',
                  confirmButtonText: 'delete',
                  cancelButtonText: 'Cancel',
                  isLoading: false,
                  onConfirmButtonPressed: () {
                    Navigator.of(context).pop();
                    context.read<ProfileBloc>().add(DeleteAccount());
                    _buildReportProfileBouttomSheet(context, state);
                  },
                  onCancelButtonPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _buildReportProfileBouttomSheet(
    BuildContext context,
    ProfileState state,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      useRootNavigator: true,
      builder: (context) => CustomBottomSheetWidget(
        child: Padding(
          // Padding to adjust for keyboard
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Enter OTP',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Pinput(
                  controller: state.otpController,
                  length: 6,
                  showCursor: true,
                  onChanged: (value) =>
                      context.read<ProfileBloc>().add(DeleteAccount()),
                  defaultPinTheme: PinTheme(
                    width: 56,
                    height: 56,
                    textStyle: const TextStyle(
                      fontSize: 20,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                buildSizedBoxH(20.h),
                CustomElevatedButton(
                  text: "Submit",
                  buttonTextStyle: Theme.of(context).textTheme.bodySmall!
                      .copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 16.sp,
                        color: Theme.of(context).customColors.fillColor,
                      ),
                  onPressed: () {
                    if (state.otpController.text.length >= 6) {
                      Navigator.of(context).pop();
                      context.read<NavBloc>().add(const NavTabChanged(0));
                      context.read<ProfileBloc>().add(ConfirmDeleteAccount());
                    }
                  },
                ),
                buildSizedBoxH(20.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildSettingsTile({
    required BuildContext context,
    required String imagePath,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(40.r),
        ),
        child: Row(
          children: [
            Container(
              height: 50.h,
              width: 50.w,
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.greycontainercolor,
                shape: BoxShape.circle,
              ),
              child: CustomImageView(
                margin: EdgeInsets.all(12.r),
                imagePath: imagePath,
                color: Theme.of(context).customColors.primaryColor,
              ),
            ),
            buildSizedboxW(12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    maxLines: subtitle != null ? 1 : 2,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (subtitle != null && subtitle.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 2.h),
                      child: Text(
                        subtitle,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).customColors.hinttextcolor,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            CustomImageView(
              imagePath: Assets.images.svgs.icons.icArrowRight.path,
            ),
          ],
        ),
      ),
    );
  }
}
