import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/auth_model/forgot_password_model.dart';
import 'package:room_eight/models/auth_model/login_model.dart';
import 'package:room_eight/models/auth_model/set_new_password_model.dart';
import 'package:room_eight/models/auth_model/signup_model.dart';
import 'package:room_eight/models/auth_model/verify_otp_model.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  static const int initialTime = 60;
  Timer? _timer;
  final AuthRepository authRepository;

  AuthBloc(this.authRepository)
    : super(
        AuthState(
          loginFormKey: GlobalKey<FormState>(),
          signupFormKey: GlobalKey<FormState>(),
          addPersonalDetailFormKey: GlobalKey<FormState>(),
          phoneController: TextEditingController(),
          phonefocusnode: FocusNode(),
          emailController: TextEditingController(),
          passwordController: TextEditingController(),
          emailFocusNode: FocusNode(),
          passwordFocusNode: FocusNode(),
          obscurePassword: true,
          signupNameController: TextEditingController(),
          signupNamefocusnode: FocusNode(),
          signupEmailController: TextEditingController(),
          signupEmailfocusnode: FocusNode(),
          signupPasswordController: TextEditingController(),
          signupPasswordfocusnode: FocusNode(),
          signupConfirmPasswordController: TextEditingController(),
          signupConfirmPasswordfocusnode: FocusNode(),
          forgotPasswordFormKey: GlobalKey<FormState>(),
          otpVerifyFormKey: GlobalKey<FormState>(),
          setNewPasswordFormKey: GlobalKey<FormState>(),
          forgotEmailController: TextEditingController(),
          verifyOtpController: TextEditingController(),
          newPasswordController: TextEditingController(),
        ),
      ) {
    on<EmailChanged>(_onEmailChanged);
    on<PasswordChanged>(_onPasswordChanged);
    on<TogglePasswordVisibility>(_onTogglePasswordVisibility);
    on<ToggleAgreementAccepted>(_onToggleAgreementAccepted);
    on<ToggleSignupPasswordVisibility>(_onToggleSignupPasswordVisibility);
    on<ToggleSignupConfirmPasswordVisibility>(
      _onToggleSignupConfirmPasswordVisibility,
    );
    on<SignupNameChanged>(_onSignupNameChanged);
    on<SignupEmailChanged>(_onSignupEmailChanged);
    on<SignupPasswordChanged>(_onSignupPasswordChanged);
    on<SignupConfirmPasswordChanged>(_onSignupConfirmPasswordChanged);
    on<LoginSubmitted>(_onLoginSubmitted);
    on<SignupSubmitted>(_onSignupSubmitted);

    on<ForgotPassword>(_onForgotPassword);
    on<VerifyOtp>(_onVerifyOtp);
    on<SetNewPassword>(_onSetNewPassword);
    on<ForgotEmailChanged>(_onForgotEmailChanged);
    on<VerifyOtpChanged>(_onVerifyOtpChanged);
    on<NewPasswordChanged>(_onNewPasswordChanged);
    on<NewConfirmPasswordChanged>(_onNewConfirmPasswordChanged);
  }

  void _onEmailChanged(EmailChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(emailController: state.emailController));
  }

  void _onPasswordChanged(PasswordChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(passwordController: state.passwordController));
  }

  void _onTogglePasswordVisibility(
    TogglePasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(obscurePassword: !state.obscurePassword));
  }

  void _onToggleSignupPasswordVisibility(
    ToggleSignupPasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(signupObscurePassword: !state.signupObscurePassword));
  }

  void _onToggleSignupConfirmPasswordVisibility(
    ToggleSignupConfirmPasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(
        signupObscureConfirmPassword: !state.signupObscureConfirmPassword,
      ),
    );
  }

  FutureOr<void> _onToggleAgreementAccepted(
    ToggleAgreementAccepted event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(isAgreementAccepted: !state.isAgreementAccepted));
  }

  void _onSignupNameChanged(SignupNameChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(signupNameController: state.signupNameController));
  }

  void _onSignupEmailChanged(
    SignupEmailChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(signupEmailController: state.signupEmailController));
  }

  void _onSignupPasswordChanged(
    SignupPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(signupPasswordController: state.signupPasswordController),
    );
  }

  void _onSignupConfirmPasswordChanged(
    SignupConfirmPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(
        signupConfirmPasswordController: state.signupConfirmPasswordController,
      ),
    );
  }

  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));
      LoginResponse loginResponse = await authRepository.loginCall(
        loginEmail: state.emailController?.text ?? "",
        loginPassword: state.passwordController?.text ?? "",
      );

      if (loginResponse.status == true) {
        Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, loginResponse.token);
        Prefobj.preferences?.put(Prefkeys.IS_USER_PROFIL_CREATED, true);

        Prefobj.preferences?.put(Prefkeys.USER_ID, loginResponse.data?.id);
        Prefobj.preferences?.put(
          Prefkeys.USER_MAIL_ID,
          loginResponse.data?.email,
        );

        state.emailController?.clear();
        state.passwordController?.clear();

        Logger.lOG("Login successful: $loginResponse");
        if (loginResponse.data?.isProfileCreated == true) {
          Prefobj.preferences?.put(Prefkeys.IS_LOGIN, true);
          NavigatorService.pushAndRemoveUntil(AppRoutes.roomEightNavBar);
        } else {
          NavigatorService.pushAndRemoveUntil(AppRoutes.welcomeScreen);
        }
      } else {
        Logger.lOG("Login failed: ${loginResponse.message}");
      }
    } catch (e) {
      Logger.lOG(e.toString());
    } finally {
      emit(state.copyWith(isloginLoading: false));
    }
  }

  Future<void> _onSignupSubmitted(
    SignupSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isSignupLoading: true));

      SignUpResponse signUpResponse = await authRepository.signUpCall(
        name: state.signupNameController?.text ?? "",
        signUpEmail: state.signupEmailController?.text ?? "",
        signUpPassword: state.signupPasswordController?.text ?? "",
      );

      Logger.lOG("Signup successful: ${signUpResponse.toString()}");

      NavigatorService.pushNamed(AppRoutes.loginScreen);

      state.signupNameController?.clear();
      state.signupEmailController?.clear();
      state.signupPasswordController?.clear();
      state.signupConfirmPasswordController?.clear();
      emit(state.copyWith(isSignupLoading: false));
    } catch (e) {
      Logger.lOG("Signup error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isSignupLoading: false));
    }
  }

  void _onForgotPassword(ForgotPassword event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(isForgotPassword: true));

      Logger.lOG(state.forgotEmailController?.text);
      final Map<String, dynamic> data = {
        "email": state.forgotEmailController?.text,
      };
      final ForgotPasswordModel result = await authRepository.forgotPassword(
        data: data,
      );
      if (result.status) {
        NavigatorService.pushNamed(AppRoutes.otpVerifyScreen);
      }
    } catch (e) {
      Logger.lOG("Signup error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isForgotPassword: false));
    }
  }

  void _onVerifyOtp(VerifyOtp event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(isVerifyOtp: true));
      Logger.lOG(state.forgotEmailController?.text);
      final Map<String, dynamic> data = {
        "email": state.forgotEmailController?.text,
        "otp": state.verifyOtpController?.text,
      };
      final VerifyOtpModel result = await authRepository.verifyOtp(data: data);
      if (result.status) {
        Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, result.token);
        Logger.lOG(Prefobj.preferences?.get(Prefkeys.AUTHTOKEN));
        NavigatorService.pushNamed(AppRoutes.setNewPasswordScreen);
      }
    } catch (e) {
      Logger.lOG("Signup error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isVerifyOtp: false));
    }
  }

  void _onSetNewPassword(SetNewPassword event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(isSetNewPassword: true));
      Logger.lOG(state.forgotEmailController?.text);
      final Map<String, dynamic> data = {
        "new_password": state.newPasswordController?.text,
      };
      final SetNewPasswordModel result = await authRepository.setNewPassword(
        data: data,
      );
      if (result.status) {
        NavigatorService.pushAndRemoveUntil(AppRoutes.loginScreen);
        state.forgotEmailController?.clear();
        state.verifyOtpController?.clear();
        state.newPasswordController?.clear();
        state.newConfirmPasswordController?.clear();
      }
    } catch (e) {
      Logger.lOG("Signup error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isSetNewPassword: false));
    }
  }

  void _onForgotEmailChanged(
    ForgotEmailChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(forgotEmailController: state.forgotEmailController));
  }

  void _onVerifyOtpChanged(VerifyOtpChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(verifyOtpController: state.verifyOtpController));
  }

  void _onNewPasswordChanged(
    NewPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(newPasswordController: state.newPasswordController));
  }

  void _onNewConfirmPasswordChanged(
    NewConfirmPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(
        newConfirmPasswordController: state.newConfirmPasswordController,
      ),
    );
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
