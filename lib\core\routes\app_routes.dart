import 'package:room_eight/views/add_personal_detail_screen/add_personal_detail_screen.dart';
import 'package:room_eight/views/add_personal_detail_screen/add_personal_detail_screen2.dart';
import 'package:room_eight/views/add_personal_detail_screen/add_personal_detail_screen3.dart';
import 'package:room_eight/views/chat/chat_screen.dart';
import 'package:room_eight/views/chat/widget/chat_image_preview.dart';
import 'package:room_eight/views/forgot_password_view/forgot_password_screen.dart';
import 'package:room_eight/views/forgot_password_view/otp_verify_screen.dart';
import 'package:room_eight/views/forgot_password_view/set_new_password_screen.dart';
import 'package:room_eight/views/like_view/like_screen.dart';
import 'package:room_eight/views/login_view/login_screen.dart';
import 'package:room_eight/views/home_view/home_screen.dart';
import 'package:room_eight/views/profile_view/edit_profile_screen.dart';
import 'package:room_eight/views/signup_view/signup_screen.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/splash_view/splash_screen.dart';
import 'package:room_eight/views/welcome_view/welcome_screen.dart';

class AppRoutes {
  static const String initialRoute = '/';
  static const String onboardingScreen = '/onboardingScreen';
  static const String loginScreen = '/loginScreen';
  static const String signupScreen = '/SignupScreen';
  static const String roomEightNavBar = '/roomEightNavBar';
  static const String notificationRoute = '/notificationScreen';
  static const String welcomeScreen = '/welcomeScreen';
  static const String addPersonalDetailScreen = '/addPersonalDetailScreen';
  static const String editProfileScreen = '/editProfileScreen';
  static const String addPersonalDetailScreen2 = '/addPersonalDetailScreen2';
  static const String addPersonalDetailScreen3 = '/addPersonalDetailScreen3';
  static const String likeScreeen = '/likeScreeen';
  static const String chatscreen = '/chatscreen';
  static const String forgotPasswordScreen = '/forgotPasswordScreen';
  static const String otpVerifyScreen = '/otpVerifyScreen';
  static const String setNewPasswordScreen = '/setNewPasswordScreen';
  static const String chatImagePreview = '/chatImagePreview';

  static Map<String, WidgetBuilder> get routes => {
    initialRoute: SplashScreen.builder,
    onboardingScreen: OnboardingScreen.builder,
    loginScreen: LoginScreen.builder,
    signupScreen: SignupScreen.builder,
    roomEightNavBar: RoomEightNavBar.builder,
    notificationRoute: HomeScreen.builder,
    welcomeScreen: WelcomeScreen.builder,
    addPersonalDetailScreen: AddPersonalDetailScreen.builder,
    addPersonalDetailScreen2: AddPersonalDetailScreen2.builder,
    addPersonalDetailScreen3: AddPersonalDetailScreen3.builder,
    editProfileScreen: EditProfileScreen.builder,
    likeScreeen: LikeScreen.builder,
    chatscreen: ChatScreen.builder,
    forgotPasswordScreen: ForgotPasswordScreen.builder,
    otpVerifyScreen: OtpVerifyScreen.builder,
    setNewPasswordScreen: SetNewPasswordScreen.builder,
    chatImagePreview: ChatImagePreview.builder,
  };
}
