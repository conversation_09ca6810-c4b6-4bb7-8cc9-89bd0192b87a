class SetNewPasswordModel {
  final bool status;
  final String message;

  SetNewPasswordModel({required this.status, required this.message});

  factory SetNewPasswordModel.fromJson(Map<String, dynamic> json) {
    return SetNewPasswordModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message};
  }
}
