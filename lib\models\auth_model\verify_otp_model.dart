class VerifyOtpModel {
  final bool status;
  final String message;
  final String token;

  VerifyOtpModel({
    required this.status,
    required this.message,
    required this.token,
  });

  factory VerifyOtpModel.fromJson(Map<String, dynamic> json) {
    return VerifyOtpModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      token: json['token'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message, 'token': token};
  }
}
