part of 'home_bloc.dart';

class HomeState extends Equatable {
  final bool loadHomePageData;
  final List<UserData> users;
  final ProfileData? userProfileDetail;
  final bool isloadUserDetail;
  final int currentIndex;
  final PageController pageController;
  final bool isPulseAnimating;
  final int carouselCurrentIndex;
  final bool hasSeenTutorial;
  final SwipableStackController? swipableController;
  final TextEditingController reportController;
  final int currentProfileId;
  final List<ProfileOptionModel> habitsLifestyle;
  final List<ProfileOptionModel> livingStyle;
  final List<ProfileOptionModel> interestsHobbies;
  final List<ProfileOptionModel> userOptionsData;
  final bool allCardsSwiped;

  const HomeState({
    this.loadHomePageData = false,
    this.users = const [],
    this.userProfileDetail,
    this.isloadUserDetail = false,
    required this.currentIndex,
    required this.pageController,
    this.isPulseAnimating = false,
    this.carouselCurrentIndex = 0,
    this.hasSeenTutorial = true,
    this.swipableController,
    required this.reportController,
    this.currentProfileId = 0,
    this.habitsLifestyle = const [],
    this.livingStyle = const [],
    this.interestsHobbies = const [],
    this.userOptionsData = const [],
    this.allCardsSwiped = false,
  });

  HomeState copyWith({
    final bool? loadHomePageData,
    List<UserData>? users,
    int? currentIndex,
    final bool? isloadUserDetail,
    PageController? pageController,
    bool? isPulseAnimating,
    int? carouselCurrentIndex,
    bool? hasSeenTutorial,
    SwipableStackController? swipableController,
    final ProfileData? userProfileDetail,
    final TextEditingController? reportController,
    final int? currentProfileId,

    final List<ProfileOptionModel>? habitsLifestyle,
    final List<ProfileOptionModel>? livingStyle,
    final List<ProfileOptionModel>? interestsHobbies,
    final List<ProfileOptionModel>? userOptionsData,
    final bool? allCardsSwiped,
  }) {
    return HomeState(
      loadHomePageData: loadHomePageData ?? this.loadHomePageData,
      users: users ?? this.users,
      isloadUserDetail: isloadUserDetail ?? this.isloadUserDetail,
      currentIndex: currentIndex ?? this.currentIndex,
      pageController: pageController ?? this.pageController,
      isPulseAnimating: isPulseAnimating ?? this.isPulseAnimating,
      carouselCurrentIndex: carouselCurrentIndex ?? this.carouselCurrentIndex,
      hasSeenTutorial: hasSeenTutorial ?? this.hasSeenTutorial,
      swipableController: swipableController ?? this.swipableController,
      userProfileDetail: userProfileDetail ?? this.userProfileDetail,
      reportController: reportController ?? this.reportController,
      userOptionsData: userOptionsData ?? this.userOptionsData,
      currentProfileId: currentProfileId ?? this.currentProfileId,
      habitsLifestyle: habitsLifestyle ?? this.habitsLifestyle,
      livingStyle: livingStyle ?? this.livingStyle,
      interestsHobbies: interestsHobbies ?? this.interestsHobbies,
      allCardsSwiped: allCardsSwiped ?? this.allCardsSwiped,
    );
  }

  @override
  List<Object?> get props => [
    loadHomePageData,
    users,
    currentIndex,
    isPulseAnimating,
    isloadUserDetail,
    carouselCurrentIndex,
    hasSeenTutorial,
    swipableController,
    userProfileDetail,
    reportController,
    currentProfileId,

    habitsLifestyle,
    livingStyle,
    interestsHobbies,
    userOptionsData,
    allCardsSwiped,
  ];
}
