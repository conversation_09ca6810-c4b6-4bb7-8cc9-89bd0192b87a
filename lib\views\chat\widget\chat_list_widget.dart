

import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/loading_animation_widget.dart';
import 'package:room_eight/viewmodels/chat_bloc/chat_bloc.dart';
import 'package:room_eight/models/chat_model/chat_list_model.dart';
import 'package:room_eight/views/chat/widget/search_user_list.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/widgets/common_widget/exception_widget.dart';
import 'package:room_eight/widgets/custom_widget/custom_transition.dart';

class UserChatListWidget extends StatefulWidget {
  final TextEditingController searchController;
  final bool showSearchField;
  final List<ChatData>? chatList;
  final ScrollController scrollController;
  final void Function() onclose;

  const UserChatListWidget({
    super.key,
    required this.searchController,
    required this.showSearchField,
    required this.scrollController,
    required this.onclose,
    this.chatList,
  });

  @override
  State<UserChatListWidget> createState() => _UserChatListWidgetState();
}

class _UserChatListWidgetState extends State<UserChatListWidget> {
  bool showCloseButton = false;

  @override
  void initState() {
    super.initState();
    widget.searchController.addListener(_updateCloseButtonVisibility);
  }

  @override
  void dispose() {
    widget.searchController.removeListener(_updateCloseButtonVisibility);
    super.dispose();
  }

  void _updateCloseButtonVisibility() {
    setState(() {
      showCloseButton = widget.searchController.text.isNotEmpty;
    });
  }

  void _closeKeyboard() {
    if (widget.searchController.text.isNotEmpty) {
      FocusScope.of(context).unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return BlocBuilder<ChatBloc, ChatState>(
          builder: (context, state) {
            final isSearchActive = widget.showSearchField &&
                widget.searchController.text.isNotEmpty;
            final hasSearchResults = state.searchuserList.isNotEmpty;
            return NotificationListener<ScrollNotification>(
              onNotification: (scrollNotification) {
                if (scrollNotification is ScrollStartNotification) {
                  _closeKeyboard();
                }
                return false;
              },
              child: Column(
                children: [
                  Expanded(
                    child: InkWell(
                      focusColor: Colors.transparent,
                      onTap: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                      },
                      child: Column(
                        children: [
                          buildSizedBoxH(10.0),
                          if (widget.showSearchField)
                            _buildSearchTextField(context),
                          if (widget.showSearchField) buildSizedBoxH(8.0),
                          buildSizedBoxH(20.0),
                          Expanded(
                            child: isSearchActive
                                ? (state.searchuserListLoading == true
                                    ? LoadingAnimationWidget()
                                    : hasSearchResults
                                        ? SearchUserListWidget(
                                            searchcontroller:
                                                widget.searchController,
                                            userList: state.searchuserList,
                                            onclose: () {
                                              final state = context
                                                  .read<ChatBloc>()
                                                  .state;
                                              if (state.chatList.isNotEmpty) {
                                                state.chatList.clear();
                                              }
                                              context.read<ChatBloc>().add(
                                                  const GetChatListEvent(
                                                      page: 1));
                                            },
                                          )
                                        : FutureBuilder(
                                            future: Future.delayed(
                                                Duration(seconds: 2)),
                                            builder: (context, snapshot) {
                                              if (snapshot.connectionState ==
                                                  ConnectionState.waiting) {
                                                return LoadingAnimationWidget();
                                              } else {
                                                return _buildNoSearchListFound(
                                                    themestate);
                                              }
                                            },
                                          ))
                                // _buildNoSearchListFound(themestate))
                                : state.isloding
                                    ? LoadingAnimationWidget()
                                    : state.chatList.isEmpty
                                        ? FutureBuilder(
                                            future: Future.delayed(
                                                Duration(seconds: 2)),
                                            builder: (context, snapshot) {
                                              if (snapshot.connectionState ==
                                                  ConnectionState.waiting) {
                                                return LoadingAnimationWidget();
                                              } else {
                                                return _buildNoChatListFound(
                                                    themestate);
                                              }
                                            },
                                          )
                                        : _buildUserList(themestate,
                                            widget.chatList ?? [], state),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildUserList(
      ThemeState themestate, List<ChatData> chatList, ChatState state) {
    return ShowUpTransition(
      forward: true,
      delay: const Duration(milliseconds: 300),
      child: Padding(
        padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewPadding.bottom + 70.h),
        child: ListView.builder(
          controller: widget.scrollController,
          physics: AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          itemCount: widget.chatList?.length,
          itemBuilder: (context, index) {
            return _buildDismissibleChatItem(themestate, chatList, index);
          },
        ),
      ),
    );
  }

  Widget _buildDismissibleChatItem(
      ThemeState themestate, List<ChatData> chatList, int index) {
    return Dismissible(
      key: Key(chatList[index].userId.toString()),
      direction: DismissDirection.endToStart,
      background: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(4.0.r),
        ),
        child: Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.w),
            child: CustomImageView(
              //TODO
              imagePath: Assets.images.svgs.icons.icClose.path,
              height: 20.0.h,
              color: ThemeData().customColors.fillColor,
            ),
          ),
        ),
      ),
      confirmDismiss: (direction) {
        return showDialog(
          context: context,
          builder: (ctx) {
            FocusScope.of(context).unfocus();
            bool isLoading = false;
            return StatefulBuilder(builder: (ctx, setState) {
              return CustomAlertDialog(
                  imagePath: Assets.images.svgs.icons.icClose.path,
                  title: 'Delete Chat',
                  // title: Lang.of(context).lbl_delete_chat,
                  subtitle: "",
                  onConfirmButtonPressed: () async {
                    setState(() {
                      isLoading = true;
                    });

                    final userId = chatList[index].userId ?? 0;
                    setState(() {
                      chatList.removeAt(index);
                    });

                    context
                        .read<ChatBloc>()
                        .add(DeleteChatApiEvent(userId: userId));
                  },
                  confirmButtonText: 'Delete',
                  // confirmButtonText: Lang.of(ctx).lbl_delete,
                  isLoading: isLoading);
            });
          },
        );
      },
      child: _buildUserDetail(context, themestate, index),
    );
  }

  Widget _buildUserDetail(
      BuildContext context, ThemeState themestate, int index) {
    Logger.lOG("is Read---- ${widget.chatList?[index].isRead}");
    return InkWell(
      onTap: () {
        FocusScope.of(context).unfocus();
        NavigatorService.pushNamed(
          AppRoutes.chatscreen,
          arguments: [
            widget.chatList?[index],
            () {
              widget.onclose();
            }
          ],
        );
        massageUserID = widget.chatList?[index].userId ?? 0;
        touser = widget.chatList?[index].touser ?? 0;
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 8.0.h),
        child: Container(
          decoration: BoxDecoration(
              color: ThemeData().customColors.primaryColor,
              borderRadius: BorderRadius.circular(40.0.r)),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.0.w, vertical: 8.0.h),
            child: Row(
              children: [
                ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 2.0.h),
                    child: CustomImageView(
                      radius: BorderRadius.circular(30.r),
                      height: 50.0.h,
                      width: 50.0.w,
                      fit: BoxFit.cover,
                      imagePath: widget.chatList![index].profileImage == null ||
                              widget.chatList![index].profileImage == ""
                          ? Assets.images.pngs.other.pngAppLogo.path
                          : "${SocketConfig.mainbaseURL}${widget.chatList![index].profileImage}",
                      alignment: Alignment.center,
                    ),
                  ),
                ),
                buildSizedboxW(8),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.chatList?[index].userName ?? '',
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                            fontSize: 16.sp,
                            fontWeight: widget.chatList?[index].isRead == false
                                ? FontWeight.bold
                                : FontWeight.w500),
                      ),
                      buildSizedBoxH(7.0),
                      Text(
                        widget.chatList?[index].latestMessage ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context)
                            .textTheme
                            .headlineSmall!
                            .copyWith(
                                fontSize: 12.sp,
                                fontWeight:
                                    widget.chatList?[index].isRead == false
                                        ? FontWeight.w800
                                        : FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                Spacer(),
                widget.chatList?[index].isRead == false
                    ? Icon(
                        Icons.circle_rounded,
                        size: 12.sp,
                        color: Theme.of(context).primaryColor,
                      )
                    : SizedBox.shrink(),
                buildSizedboxW(10.w)
              ],
            ),
          ),
        ),

        // Row(
        //   children: [
        //     Container(
        //       height: 50.0.h,
        //       width: 50.0.w,
        //       decoration: BoxDecoration(
        //         borderRadius: BorderRadius.circular(14.r),
        //         border: Border.all(
        //           color: Theme.of(context).textTheme.titleSmall!.color!,
        //           width: 2.w,
        //         ),
        //       ),
        //       child: ClipRRect(
        //         clipBehavior: Clip.hardEdge,
        //         child: Padding(
        //           padding: const EdgeInsets.all(4.0),
        //           child: CustomImageView(
        //             radius: BorderRadius.circular(10.r),
        //             height: 50.0.h,
        //             width: 50.0.w,
        //             fit: BoxFit.cover,
        //             imagePath: widget.chatList![index].profileImage == null ||
        //                     widget.chatList![index].profileImage == ""
        //                 ? Assets.images.png.other.pngPlaceHolder.path
        //                 : "${APIEndPoints.mainbaseURL}${widget.chatList![index].profileImage}",
        //             alignment: Alignment.center,
        //           ),
        //         ),
        //       ),
        //     ),
        //     buildSizedboxW(8),
        //     Flexible(
        //       child: InkWell(
        //         onTap: () {
        // FocusScope.of(context).unfocus();
        // NavigatorService.pushNamed(AppRoutes.chatscreen, arguments: [
        //   widget.chatList?[index],
        //   () {
        //     widget.onclose();
        //   }
        // ]);
        //         },
        //         child:

        //          Column(
        //           crossAxisAlignment: CrossAxisAlignment.start,
        //           children: [
        //             Text(
        //               widget.chatList?[index].userName ?? '',
        // style: Theme.of(context)
        //     .textTheme
        //     .titleLarge!
        //     .copyWith(fontSize: 13.sp, fontWeight: FontWeight.w700),
        //             ),
        //             buildSizedBoxH(6.0),
        //             Row(
        //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //               mainAxisSize: MainAxisSize.max,
        //               children: [
        // Flexible(
        //   child: Text(
        //     widget.chatList?[index].latestMessage ?? '',
        //     maxLines: 1,
        //     overflow: TextOverflow.ellipsis,
        //     style: Theme.of(context)
        //         .textTheme
        //         .headlineSmall!
        //         .copyWith(fontSize: 12.sp),
        //   ),
        // ),
        //                 Column(
        //                   mainAxisAlignment: MainAxisAlignment.center,
        //                   children: [
        // Text(
        //   DateTime.parse(
        //           widget.chatList![index].createdAt ?? '')
        //       .createdTimeAgo(),
        //   style: Theme.of(context)
        //       .textTheme
        //       .headlineSmall!
        //       .copyWith(fontSize: 11.sp),
        // ),
        //                   ],
        //                 ),
        //               ],
        //             ),
        //           ],
        //         ),
        //       ),
        //     ),
        //   ],
        // ),
      ),
    );
  }

  // Widget _buildSearchTextField(ThemeState themestate) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(horizontal: 12.0.w),
  //     child: SizedBox(
  //       height: 50.0.h,
  //       child: FlowkarTextFormField(
  //         context: context,
  //         controller: widget.searchController,
  //         fillColor: AppColors.searchtextfieldcolor,
  //         contentPadding: const EdgeInsets.all(12.0),
  //         filled: true,
  //         isLabelEnabled: false,
  //         labelText: Lang.of(context).lbl_search,
  //         onChanged: (searchtext) {
  // context
  //     .read<ChatBloc>()
  //     .add(SearchUserListEvent(searchtext: searchtext));
  //         },
  //         prefix: CustomImageView(
  //           color: themestate.isDarkThemeOn ? AppColors.whitecolor : null,
  //           imagePath: Assets.images.icons.icSearch.path,
  //           margin: const EdgeInsets.all(16.0),
  //           height: 16.0.h,
  //           width: 16.0.w,
  //         ),
  //         suffix: Visibility(
  //           visible: showCloseButton,
  //           child: IconButton(
  //             icon: CustomImageView(
  //               color: themestate.isDarkThemeOn ? AppColors.whitecolor : null,
  //               height: 16.0.h,
  //               width: 16.0.w,
  //               imagePath: Assets.images.icons.icClose.path,
  //             ),
  //             onPressed: () {
  //               widget.searchController.clear();
  //               FocusScope.of(context).unfocus();
  //             },
  //           ),
  //         ),
  //         inputAction: TextInputAction.done,
  //       ),
  //     ),
  //   );
  // }
  Widget _buildSearchTextField(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: ValueListenableBuilder<TextEditingValue>(
        valueListenable: widget.searchController,
        builder: (context, value, child) {
          return CustomTextInputField(
            hintLabel: Lang.of(context).lbl_search,
            context: context,
            controller: widget.searchController,
            prefixIcon: CustomImageView(
              imagePath: Assets.images.svgs.icons.icSearch.path,
              height: 20.0.h,
              margin: EdgeInsets.all(16.0),
            ),
            onChanged: (value) {
              context
                  .read<ChatBloc>()
                  .add(SearchUserListEvent(searchtext: value));
            },
            fillColor: ThemeData().customColors.fillColor,
            
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
            suffixIcon: value.text.isNotEmpty
                ? CustomImageView(
                    imagePath: Assets.images.svgs.icons.icClose.path,
                    height: 15.0.h,
                    margin: EdgeInsets.all(16.0),
                    onTap: () {
                      widget.searchController.clear();
                      FocusManager.instance.primaryFocus?.unfocus();
                    },
                  )
                : null, type: InputType.text,
          );
        },
      ),
    );
  }

  Widget _buildNoChatListFound(ThemeState themestate) {
    return ExceptionWidget(
      imagePath: Assets.images.svgs.icons.icClose.path,
      showButton: false,
      title: 'Lang.of(context).lbl_no_message',
      subtitle: 'Lang.of(context).lbl_no_message_subtitle',
      // title: Lang.of(context).lbl_no_message,
      // subtitle: Lang.of(context).lbl_no_message_subtitle,
    );
  }

  Widget _buildNoSearchListFound(ThemeState themestate) {
    return Center(
        child: ExceptionWidget(
      imagePath: Assets.images.svgs.icons.icClose.path,
      title: 'Lang.of(context).lbl_no_result_found',
      subtitle: 'Lang.of(context).lbl_no_result_found_subtitle',
      showButton: false,
    ));
  }
}
