part of 'like_bloc.dart';

class LikeState extends Equatable {
  final int curentTebIndex;
  final List<MoveInUser> moveInData;
  final List<MoveOutUser> moveOutData;
  final bool isLoadingMoveOut;
  final bool isLoadingMoveIn;
  final bool isAcceptingLike;
  final bool shouldNavigateToChat;
  final MoveInUser? acceptedUser;
  final int? toMessageId;

  const LikeState({
    this.curentTebIndex = 0,
    this.isLoadingMoveOut = false,
    this.isLoadingMoveIn = false,
    this.isAcceptingLike = false,
    this.shouldNavigateToChat = false,
    this.acceptedUser,
    this.toMessageId,
    this.moveInData = const [],
    this.moveOutData = const [],
  });

  @override
  List<Object?> get props => [
    curentTebIndex,
    isLoadingMoveOut,
    isLoadingMoveIn,
    isAcceptingLike,
    shouldNavigateToChat,
    acceptedUser,
    toMessageId,
    moveInData,
    moveOutData,
  ];

  LikeState copyWith({
    final int? curentTebIndex,
    final bool? isLoadingMoveOut,
    final bool? isLoadingMoveIn,
    final bool? isAcceptingLike,
    final bool? shouldNavigateToChat,
    final MoveInUser? acceptedUser,
    final int? toMessageId,
    final List<MoveInUser>? moveInData,
    final List<MoveOutUser>? moveOutData,
  }) {
    return LikeState(
      curentTebIndex: curentTebIndex ?? this.curentTebIndex,
      isLoadingMoveOut: isLoadingMoveOut ?? this.isLoadingMoveOut,
      isLoadingMoveIn: isLoadingMoveIn ?? this.isLoadingMoveIn,
      isAcceptingLike: isAcceptingLike ?? this.isAcceptingLike,
      shouldNavigateToChat: shouldNavigateToChat ?? this.shouldNavigateToChat,
      acceptedUser: acceptedUser ?? this.acceptedUser,
      toMessageId: toMessageId ?? this.toMessageId,
      moveInData: moveInData ?? this.moveInData,
      moveOutData: moveOutData ?? this.moveOutData,
    );
  }

  LikeState resetNavigation() {
    return copyWith(
      shouldNavigateToChat: false,
      acceptedUser: null,
      toMessageId: null,
    );
  }
}
