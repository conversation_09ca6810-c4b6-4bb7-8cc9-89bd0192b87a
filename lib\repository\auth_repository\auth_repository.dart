import 'package:flutter/foundation.dart';
import 'package:room_eight/core/api_config/client/api_client.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/models/auth_model/forgot_password_model.dart';
import 'package:room_eight/models/auth_model/login_model.dart';
import 'package:room_eight/models/auth_model/set_new_password_model.dart';
import 'package:room_eight/models/auth_model/signup_model.dart';
import 'package:room_eight/models/auth_model/verify_otp_model.dart';

class AuthRepository {
  final ApiClient apiClient;
  AuthRepository({required this.apiClient});

  Future<SignUpResponse> signUpCall({
    required String name,
    required String signUpEmail,
    required String signUpPassword,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'name': name,
        'email': signUpEmail,
        'password': signUpPassword,
      };
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.signUpUrl,
        data: data,
      );
      return await compute(SignUpResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<LoginResponse> loginCall({
    required String loginEmail,
    required String loginPassword,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'email': loginEmail,
        'password': loginPassword,
      };
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.loginUpUrl,
        data: data,
      );
      return await compute(LoginResponse.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<ForgotPasswordModel> forgotPassword({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.forgotPassword,
        data: data,
      );
      return await compute(ForgotPasswordModel.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<VerifyOtpModel> verifyOtp({required Map<String, dynamic> data}) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.verifyOtp,

        data: data,
      );
      return await compute(VerifyOtpModel.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }

  Future<SetNewPasswordModel> setNewPassword({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.request(
        RequestType.POST,
        ApiEndPoint.setNewPassword,
        data: data,
      );
      return await compute(SetNewPasswordModel.fromJson, response);
    } catch (error) {
      rethrow;
    }
  }
}
