Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter --quiet assemble --no-version-check --depfile D:\project\CFA-ROOM8-APP\build\app\intermediates\flutter\devDebug/flutter_build.d --output D:\project\CFA-ROOM8-APP\build\app\intermediates\flutter\devDebug -dTargetFile=D:\project\CFA-ROOM8-APP\lib\main\main_dev.dart -dTargetPlatform=android -dBuildMode=debug -dTrackWidgetCreation=true --DartDefines=RkxVVFRFUl9BUFBfRkxBVk9SPWRldg==,RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= -dFlavor=dev -dAndroidArchs=android-x64 -dMinSdkVersion=21 debug_android_application

## exception

PathAccessException: PathAccessException: Cannot delete file, path = 'D:\project\CFA-ROOM8-APP\build\app\intermediates\flutter\devDebug\flutter_assets\kernel_blob.bin' (OS Error: The process cannot access the file because it is being used by another process.
, errno = 32)

```
#0      _File.throwIfError (dart:io/file_impl.dart:782:7)
#1      _File._deleteSync (dart:io/file_impl.dart:367:5)
#2      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:425:7)
#3      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#4      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#5      ErrorHandlingFileSystem.deleteIfExists (package:flutter_tools/src/base/error_handling_io.dart:90:14)
#6      FlutterBuildSystem.trackSharedBuildDirectory (package:flutter_tools/src/build_system/build_system.dart:777:33)
#7      FlutterBuildSystem.build (package:flutter_tools/src/build_system/build_system.dart:658:5)
<asynchronous suspension>
#8      AssembleCommand.runCommand (package:flutter_tools/src/commands/assemble.dart:346:32)
<asynchronous suspension>
#9      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1563:27)
<asynchronous suspension>
#10     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#11     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#12     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#14     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#15     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#17     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.32.5, on Microsoft Windows [Version 10.0.22631.5624], locale en-IN) [1,889ms]
    • Flutter version 3.32.5 on channel stable at D:\flutter_sdk\flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision fcf2c11572 (5 weeks ago), 2025-06-24 11:44:07 -0700
    • Engine revision dd93de6fb1
    • Dart version 3.8.1
    • DevTools version 2.45.1

[✓] Windows Version (11 Pro 64-bit, 23H2, 2009) [20.5s]

[!] Android toolchain - develop for Android devices (Android SDK version 36.0.0) [15.5s]
    • Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    • Platform android-36, build-tools 36.0.0
    • Java binary at: C:\Program Files\Android\Android Studio1\jbr\bin\java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    • Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)
    ! Some Android licenses not accepted. To resolve this, run: flutter doctor --android-licenses

[✓] Chrome - develop for the web [20ms]
    • Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[✗] Visual Studio - develop Windows apps [18ms]
    ✗ Visual Studio not installed; this is necessary to develop Windows apps.
      Download at https://visualstudio.microsoft.com/downloads/.
      Please install the "Desktop development with C++" workload, including all of its default components

[✓] Android Studio (version 2024.3.2) [15ms]
    • Android Studio at C:\Program Files\Android\Android Studio1
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)

[✓] VS Code (version 1.102.2) [12ms]
    • VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    • Flutter extension version 3.114.0

[✓] Connected device (5 available) [9.3s]
    • V2207 (mobile)                • 10BCA811VT000NA • android-arm64  • Android 14 (API 34)
    • sdk gphone16k x86 64 (mobile) • emulator-5554   • android-x64    • Android 16 (API 36) (emulator)
    • Windows (desktop)             • windows         • windows-x64    • Microsoft Windows [Version 10.0.22631.5624]
    • Chrome (web)                  • chrome          • web-javascript • Google Chrome 138.0.7204.169
    • Edge (web)                    • edge            • web-javascript • Microsoft Edge 138.0.3351.109

[✓] Network resources [4.5s]
    • All expected network resources are available.

! Doctor found issues in 2 categories.
```
