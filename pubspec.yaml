name: room_eight
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  flutter_bloc: ^9.1.0
  dio: ^5.8.0+1

  cupertino_icons: ^1.0.8
  path_provider: ^2.1.5
  hive: ^2.2.3
  connectivity_plus: ^6.1.3
  one_context: ^4.1.0
  animated_splash_screen: ^1.3.0
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1
  lottie: ^3.3.1
  flutter_svg: ^2.0.17
  uuid: ^4.5.1
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  shimmer: ^3.0.0
  validators: ^3.0.0
  country_picker: ^2.0.27
  equatable: ^2.0.7
  dotted_line: ^3.2.3
  toastification: ^2.3.0
  flutter_native_splash: ^2.4.5
  pretty_dio_logger: ^1.4.0
  flutter_dotenv: ^5.2.1
  device_info_plus: ^11.3.3
  pinput: ^5.0.1
  intl: ^0.20.0
  syncfusion_flutter_charts: ^30.1.38
  flutter_slidable: ^3.0.0
  smooth_page_indicator: ^1.2.1
  persistent_bottom_nav_bar: ^6.2.1
  image_picker: ^1.1.2
  swipable_stack: ^2.0.0
  carousel_slider: ^5.1.1
  geolocator: ^14.0.2
  http: ^1.4.0
  permission_handler: ^11.3.0
  flutter_launcher_icons: ^0.14.4
  flutter_card_swiper: ^7.0.2
  readmore: ^3.0.0
  socket_io_client: ^3.0.2
  loading_indicator: ^3.1.1
  table_calendar: ^3.2.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/pngs/other/app_launcher_logo.png" 
  
    # Code Generation
flutter_gen:
  line_length: 160
  integrations:
    flutter_svg: true
    lottie: true
  assets:
    enabled: true
  output: lib/core/generated

dev_dependencies:
  flutter_test:
    sdk: flutter
    # Code Generation
  build_runner: ^2.4.13
  flutter_lints: ^5.0.0
  intl_utils: ^2.8.7
  flutter_gen_runner: ^5.8.0

flutter_intl:
  enabled: true
  class_name: Lang
  main_locale: en
  arb_dir: lib/core/l10n 
  output_dir: lib/core/generated

flutter:
  uses-material-design: true
  assets:
    - .env
    - assets/images/pngs/other/
    - assets/images/pngs/icons/
    - assets/images/svgs/icons/
    - assets/images/svgs/other/
    - assets/lottie/
    
  


     
  fonts:
  - family: Gilroy
    fonts:
      - asset: assets/fonts/gilroy-light.ttf
        weight: 300
      - asset: assets/fonts/gilroy-regular.ttf
        weight: 400
      - asset: assets/fonts/gilroy-medium.ttf
        weight: 500
      - asset: assets/fonts/gilroy-semibold.ttf
        weight: 600  
      - asset: assets/fonts/gilroy-bold.ttf
        weight: 700
      - asset: assets/fonts/gilroy-black.ttf
        weight: 900         
            
flutter_native_splash:
  color: "#FFFFFF"
  android: true 
  ios: true   