ChatListModel deserializeChatListModel(Map<String, dynamic> json) =>
    ChatListModel.fromJson(json);

class ChatListModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  ChatListModel({this.count, this.next, this.previous, this.results});

  ChatListModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results =
        json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class Results {
  bool? status;
  String? message;
  List<ChatData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <ChatData>[];
      json['data'].forEach((v) {
        data!.add(ChatData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ChatData {
  int? userId;
  int? touser;
  String? profileImage;
  String? userName;
  String? latestMessage;
  String? createdAt;
  bool? isRead;

  ChatData({
    this.userId,
    this.touser,
    this.profileImage,
    this.userName,
    this.latestMessage,
    this.createdAt,
    this.isRead,
  });

  ChatData.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    touser = json['to_user'];
    profileImage = json['profile_image'];
    userName = json['user_name'];
    latestMessage = json['latest_message'];
    createdAt = json['created_at'];
    isRead = json['is_read'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['to_user'] = touser;
    data['profile_image'] = profileImage;
    data['user_name'] = userName;
    data['latest_message'] = latestMessage;
    data['created_at'] = createdAt;
    data['is_read'] = isRead;
    return data;
  }
}
