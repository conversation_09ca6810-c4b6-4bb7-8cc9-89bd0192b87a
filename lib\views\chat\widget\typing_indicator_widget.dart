import 'dart:math';

import 'package:room_eight/core/utils/app_exports.dart';

class TypingIndicator extends StatefulWidget {
  const TypingIndicator({
    super.key,
    this.showIndicator = false,
    this.userrname = '',
    //TODO: PRIMARY
    this.bubblecolor = Colors.black,
  });
  final bool showIndicator;
  final Color bubblecolor;
  final String userrname;

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _appearanceController;

  late Animation<double> _indicatorSpaceAnimation;

  late Animation<double> _largeBubbleAnimation;

  late AnimationController _repeatingController;
  final List<Interval> _dotIntervals = const [
    Interval(0.25, 0.8),
    Interval(0.35, 0.9),
    Interval(0.45, 1.0),
  ];

  final List<AnimationController> _jumpControllers = [];
  final List<Animation> _jumpAnimations = [];

  double get indicatorSize => 4; // Reduced from 6 to 4

  double get indicatorSpacing =>
      3; // Reduced from 4 to 3 for proportional spacing

  Color? get flashingCircleDarkColor => Colors.black;
  //TODO: COLOR

  Color? get flashingCircleBrightColor => Colors.black;

  @override
  void initState() {
    super.initState();
    if (mounted) _initializeAnimationController();
  }

  void _initializeAnimationController() {
    _appearanceController =
        AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 750), // Add missing duration
        )..addListener(() {
          setState(() {});
        });

    _indicatorSpaceAnimation = CurvedAnimation(
      parent: _appearanceController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
      reverseCurve: const Interval(0.0, 1.0, curve: Curves.easeOut),
    ).drive(Tween<double>(begin: 0.0, end: 50.0)); // Reduced from 60.0 to 50.0

    _largeBubbleAnimation = CurvedAnimation(
      parent: _appearanceController,
      curve: const Interval(0.3, 1.0, curve: Curves.elasticOut),
      reverseCurve: const Interval(0.5, 1.0, curve: Curves.easeOut),
    );

    _repeatingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    for (int i = 0; i < 3; i++) {
      _jumpControllers.add(
        AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 500),
          reverseDuration: const Duration(milliseconds: 500),
        ),
      );
      _jumpAnimations.add(
        CurvedAnimation(
          parent: _jumpControllers[i],
          curve: Interval((0.2 * i), 0.7, curve: Curves.easeOutSine),
          reverseCurve: Interval((0.2 * i), 0.7, curve: Curves.easeOut),
        ).drive(
          Tween<double>(begin: 0, end: 8),
        ), // Reduced jump height from 10 to 8
      );
    }

    if (widget.showIndicator) {
      _showIndicator();
    }
  }

  @override
  void didUpdateWidget(TypingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    Logger.lOG(
      "TypingIndicator didUpdateWidget: old=${oldWidget.showIndicator}, new=${widget.showIndicator}",
    );

    if (widget.showIndicator != oldWidget.showIndicator) {
      if (widget.showIndicator) {
        Logger.lOG("TypingIndicator: Showing indicator");
        _showIndicator();
      } else {
        Logger.lOG("TypingIndicator: Hiding indicator");
        _hideIndicator();
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _appearanceController.dispose();
    _repeatingController.dispose();
    for (var element in _jumpControllers) {
      element.dispose();
    }
    super.dispose();
  }

  void _showIndicator() {
    Logger.lOG("TypingIndicator: _showIndicator called");
    _appearanceController
      ..duration = const Duration(milliseconds: 750)
      ..forward();
    _repeatingController.repeat();
    for (int i = 0; i < 3; i++) {
      _jumpControllers[i].repeat(reverse: true);
    }
    Logger.lOG("TypingIndicator: _showIndicator animations started");
  }

  void _hideIndicator() {
    Logger.lOG("TypingIndicator: _hideIndicator called");
    _appearanceController
      ..duration = const Duration(milliseconds: 150)
      ..reverse();
    _repeatingController.stop();
    for (int i = 0; i < 3; i++) {
      _jumpControllers[i].stop();
    }
    Logger.lOG("TypingIndicator: _hideIndicator animations stopped");
  }

  @override
  Widget build(BuildContext context) {
    Logger.lOG(
      "TypingIndicator build called - showIndicator: ${widget.showIndicator}",
    );
    return AnimatedBuilder(
      animation: _indicatorSpaceAnimation,
      builder: (context, child) {
        Logger.lOG(
          "TypingIndicator AnimatedBuilder - height: ${_indicatorSpaceAnimation.value}",
        );
        return SizedBox(height: _indicatorSpaceAnimation.value, child: child);
      },
      child: Stack(
        children: [
          _buildAnimatedBubble(
            animation: _largeBubbleAnimation,
            left: 5,
            bottom: 12,
            bubble: _buildStatusBubble(),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBubble({
    required Animation<double> animation,
    required double left,
    required double bottom,
    required Widget bubble,
  }) {
    return Positioned(
      left: left,
      bottom: bottom,
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          return Transform.scale(
            scale: animation.value,
            alignment: Alignment.centerLeft,
            child: child,
          );
        },
        child: Row(children: [bubble]),
      ),
    );
  }

  Widget _buildStatusBubble() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.fromLTRB(
            4.0.w,
            0.h,
            2.0.w,
            10.0.h,
          ), // Reduced padding
          margin: EdgeInsets.fromLTRB(5.0.w, 0.h, 2.w, 2.h),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 8.0,
            ), // Reduced from 10.0 to 8.0
            child: Row(
              children: [
                _bubbleJumpAnimation(2, 0),
                _bubbleJumpAnimation(1, 1),
                _bubbleJumpAnimation(0, 2),
              ],
            ),
          ),
        ),
        // Text(
        //   "${widget.userrname} ${'typing..'}",
        //   style: Theme.of(
        //     context,
        //   ).textTheme.bodySmall?.copyWith(fontSize: 11.0.sp),
        // ),
      ],
    );
  }

  Widget _bubbleJumpAnimation(int value, int index) {
    return AnimatedBuilder(
      animation: _jumpAnimations[value],
      builder: (context, child) {
        final circleFlashPercent = _dotIntervals[index].transform(
          _repeatingController.value,
        );
        final circleColorPercent = sin(pi * circleFlashPercent);
        return Transform.translate(
          offset: Offset(0, _jumpAnimations[value].value),
          child: Container(
            width: indicatorSize,
            height: indicatorSize,
            margin: EdgeInsets.symmetric(horizontal: indicatorSpacing),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Color.lerp(
                flashingCircleDarkColor,
                flashingCircleBrightColor,
                circleColorPercent,
              ),
            ),
          ),
        );
      },
    );
  }
}
