import 'dart:ui';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/views/add_personal_detail_screen/widget/add_personal_detail_screen3_shimmer.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class AddPersonalDetailScreen3 extends StatelessWidget {
  const AddPersonalDetailScreen3({super.key});

  static Widget builder(BuildContext context) => AddPersonalDetailScreen3();

  @override
  Widget build(BuildContext context) {
    // context.read<ProfileBloc>().add(GetSelectionOption());
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Theme.of(context).customColors.fillColor,
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          if (state.profileLoading == true) {
            return AddPersonalDetailScreen3Shimmer();
          }
          return AbsorbPointer(
            absorbing: state.isEditProfileLoading,
            child: BackgroundImage(
              imagePath: Assets.images.pngs.other.pngAuthBg4.path,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildTopSection(context),
                      buildSizedBoxH(25.h),
                      Expanded(child: _buildSelectionMenuLists(context, state)),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 16.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            NavigatorService.goBack();
          },
          child: CustomGradientContainer(
            height: 36.w,
            width: 36.w,
            topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
            bottomColor: customColors.blackColor!.withAlpha(
              (0.4 * 255).toInt(),
            ),
            fillColor: customColors.fillColor!.withAlpha(75),
            child: CustomImageView(
              imagePath: Assets.images.svgs.icons.icBackArrow.path,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionMenuLists(BuildContext context, ProfileState state) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).customColors.fillColor?.withValues(alpha: 0.8),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDragHandle(context),
                buildSizedBoxH(24.h),
                Expanded(child: _buildMenuDetails(context, state)),
                buildSizedBoxH(24.h),
                // const Spacer(),
                _buildProfileButton(context, state),
                // buildSizedBoxH(16.h),
                // _buildSignUpOption(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Column(
      children: [
        Text(
          Lang.of(context).lbl_find_the_right_room8,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 22.sp,
            color: Theme.of(context).customColors.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        buildSizedBoxH(4.h),
        Text(
          Lang.of(context).lbl_your_personal_details_desc3,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 14.sp,
            color: Theme.of(context).customColors.darkGreytextcolor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuDetails(BuildContext context, ProfileState state) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgreementCheckbox(context, state),
          buildSizedBoxH(12.h),
          CustomTextInputField(
            context: context,
            type: InputType.text,
            hintLabel: Lang.of(context).lbl_what_are_your_interests,
            controller: state.searchController,
            focusNode: state.fullNameFocusNode,
            textInputAction: TextInputAction.next,
            prefixIcon: CustomImageView(
              imagePath: Assets.images.svgs.icons.icSearch.path,
              margin: EdgeInsets.all(10),
              color: Theme.of(context).customColors.primaryColor,
            ),
            onChanged: (value) =>
                context.read<ProfileBloc>().add(SearchChanged(value)),
          ),
          buildSizedBoxH(12.h),
          _builsLabel(context, Lang.of(context).lbl_habits_lifestyle),
          _buildOptionsList(
            context: context,
            options: state.habitsLifestyle,
            selectedHabits: state.selectedHabitsAndLifestyle,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectHabitsAndLifestyle(habitsAndLifestyle: name),
              );
            },
          ),
          buildSizedBoxH(12.h),
          _builsLabel(context, Lang.of(context).lbl_cleanliness_living_style),
          _buildOptionsList(
            context: context,
            options: state.livingStyle,
            selectedHabits: state.selectedCleanlinessLivingStyle,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectCleanlinessLivingStyle(cleanlinessLivingStyle: name),
              );
            },
          ),
          buildSizedBoxH(12.h),
          _builsLabel(context, Lang.of(context).lbl_interests_hobbies),
          _buildOptionsList(
            context: context,
            options: state.interestsHobbies,
            selectedHabits: state.selectedInterestsHobbies,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectInterestsHobbies(interestsHobbies: name),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionDesign(
    BuildContext context,
    String text,
    String image,
    bool isSelected,
    void Function()? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Chip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: ApiEndPoint.getImageUrl + image,
              height: 20.h,
              width: 20.w,
            ),
            buildSizedboxW(5.w),
            Text(
              text,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isSelected
                    ? Theme.of(context).customColors.fillColor
                    : Theme.of(context).customColors.darkGreytextcolor,
                fontSize: 16.0.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: isSelected
            ? Theme.of(context).customColors.primaryColor
            : Theme.of(context).customColors.fillColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r), // Change radius as needed
          side: BorderSide(color: Colors.transparent, width: 0), // No border
        ),
      ),
    );
  }

  Widget _buildOptionsList({
    required BuildContext context,
    required List<ProfileOptionModel> options,
    required List<int> selectedHabits,
    required void Function(String name) onSelect,
  }) {
    return Wrap(
      children: List.generate(
        options.length,
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildOptionDesign(
            context,
            options[index].name ?? '',
            options[index].icon ?? '',
            selectedHabits.contains(options[index].id),
            () => onSelect(options[index].name ?? ''),
          ),
        ),
      ),
    );
  }

  Widget _buildAgreementCheckbox(BuildContext context, ProfileState state) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 20.h,
          child: Checkbox(
            value: true, // Always checked
            onChanged: null, // Not changeable
            activeColor: Theme.of(context).customColors.greencolor,
            checkColor: Theme.of(context).customColors.fillColor,
          ),
        ),
        buildSizedboxW(8.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontSize: 14.sp,
                color: Theme.of(context).customColors.darkGreytextcolor,
                height: 1.5,
              ),
              children: [
                TextSpan(
                  text: '${Lang.of(context).lbl_pick_5_things} ',
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).customColors.darkGreytextcolor,
                    fontSize: 14.sp,
                  ),
                ),
                TextSpan(
                  text: Lang.of(
                    context,
                  ).lbl_that_matter_to_you_when_living_with_a_room8,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontSize: 14.sp,
                    color: Theme.of(context).customColors.darkGreytextcolor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _builsLabel(BuildContext context, String label) {
    return Text(
      label,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 16.sp,
        color: Theme.of(context).customColors.blackColor,
      ),
    );
  }

  Widget _buildProfileButton(BuildContext context, ProfileState state) {
    final isValid =
        state.selectedHabitsAndLifestyle.isNotEmpty &&
        state.selectedCleanlinessLivingStyle.isNotEmpty &&
        state.selectedInterestsHobbies.isNotEmpty;
    return CustomElevatedButton(
      isLoading: state.isEditProfileLoading,
      isDisabled:
          state.isEditProfileLoading || !isValid || state.selectedOption <= 4,
      text: " ${state.selectedOption}/5 ${Lang.of(context).lbl_selected}",
      buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Theme.of(context).customColors.fillColor,
        fontSize: 18.0.sp,
        fontWeight: FontWeight.w500,
      ),
      onPressed: () {
        FocusManager.instance.primaryFocus?.unfocus();

        if (state.selectedOption >= 5) {
          context.read<ProfileBloc>().add(FinalProfileSubmitted());
        }
      },
    );
  }
}
