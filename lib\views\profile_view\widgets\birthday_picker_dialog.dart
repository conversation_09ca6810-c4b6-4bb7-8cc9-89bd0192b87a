import 'package:room_eight/core/utils/app_exports.dart';
import 'package:table_calendar/table_calendar.dart';

class BirthdayPickerDialog extends StatefulWidget {
  final DateTime? initialDate;
  const BirthdayPickerDialog({super.key, this.initialDate});

  @override
  State<BirthdayPickerDialog> createState() => _BirthdayPickerDialogState();
}

class _BirthdayPickerDialogState extends State<BirthdayPickerDialog>
    with TickerProviderStateMixin {
  late DateTime selectedDate;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    selectedDate = widget.initialDate ?? DateTime(2000, 1, 1);

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onCancel() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.pop(context);
    }
  }

  void _onSelect() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.pop(context, selectedDate);
    }
  }

  int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final age = _calculateAge(selectedDate);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 24,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(28),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.15),
                  blurRadius: 24,
                  spreadRadius: 0,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDark ? 0.15 : 0.08),
                  blurRadius: 6,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Text(
                    'Select Your Birthday',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),

                  buildSizedBoxH(8),

                  // Age display
                  Text(
                    'Age: $age years old',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).customColors.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  buildSizedBoxH(24),

                  // Date picker
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Theme.of(
                        context,
                      ).customColors.fillColor?.withAlpha(76), // ~0.3 opacity
                    ),
                    child: TableCalendar(
                      firstDay: DateTime(1950),
                      lastDay: DateTime.now().subtract(
                        const Duration(days: 365 * 16),
                      ), // Must be at least 16 years old
                      focusedDay: selectedDate,
                      selectedDayPredicate: (day) {
                        return isSameDay(selectedDate, day);
                      },
                      onDaySelected: (selected, focused) {
                        setState(() {
                          selectedDate = selected;
                        });
                        HapticFeedback.selectionClick();
                      },
                      calendarStyle: CalendarStyle(
                        isTodayHighlighted: false,
                        selectedDecoration: BoxDecoration(
                          color: Theme.of(context)
                              .customColors
                              .primaryColor, // 🎨 Your selected date background color
                          shape: BoxShape.circle,
                        ),
                        selectedTextStyle: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(
                              color: Theme.of(context).customColors.fillColor,
                              fontWeight: FontWeight.bold,
                            ),
                        defaultTextStyle: Theme.of(
                          context,
                        ).textTheme.bodyMedium!,
                        weekendTextStyle: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(
                              color: Theme.of(
                                context,
                              ).customColors.primaryColor,
                            ),
                      ),
                      headerStyle: HeaderStyle(
                        formatButtonVisible: false,
                        titleCentered: true,
                        titleTextStyle: Theme.of(
                          context,
                        ).textTheme.titleMedium!,
                        leftChevronIcon: Icon(
                          Icons.chevron_left,
                          color: Theme.of(context).iconTheme.color,
                        ),
                        rightChevronIcon: Icon(
                          Icons.chevron_right,
                          color: Theme.of(context).iconTheme.color,
                        ),
                      ),
                      calendarFormat: CalendarFormat.month,
                      availableGestures: AvailableGestures.horizontalSwipe,
                      rowHeight: 48,
                    ),
                  ),

                  buildSizedBoxH(24),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: CustomElevatedButton(
                          onPressed: _onCancel,
                          text: 'Cancel',
                          secondary: true,
                          buttonStyle: ButtonThemeHelper.secondaryButtonStyle(
                            context,
                          ),
                        ),
                      ),
                      buildSizedboxW(12),
                      Expanded(
                        child: CustomElevatedButton(
                          onPressed: _onSelect,
                          text: 'Confirm',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
