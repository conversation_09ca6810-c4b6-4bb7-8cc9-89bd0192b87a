import 'package:room_eight/core/utils/app_exports.dart';

class CustomImageView extends StatelessWidget {
  ///[imagePath] is required parameter for showing image

  // Static cache manager instance to handle corrupted cache gracefully
  static CacheManager? _cacheManager;

  final String? imagePath;
  final double? height;
  final double? width;
  final Color? color;
  final BoxFit? fit;
  final Alignment? alignment;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? radius;
  final BoxBorder? border;
  final BlendMode? colorBlendMode;
  final bool usePlaceholder;
  final Duration fadeInDuration;
  final Duration fadeOutDuration;

  const CustomImageView({
    super.key,
    this.imagePath,
    this.height,
    this.width,
    this.color,
    this.fit,
    this.alignment,
    this.onTap,
    this.radius,
    this.margin,
    this.border,
    this.colorBlendMode,
    this.usePlaceholder = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.fadeOutDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return alignment != null
        ? Align(alignment: alignment!, child: _buildWidget())
        : _buildWidget();
  }

  /// Get cache manager instance with error handling for corrupted cache
  static CacheManager getCacheManager() {
    if (_cacheManager != null) {
      return _cacheManager!;
    }

    try {
      _cacheManager = CacheManager(
        Config(
          'RoomEight_V2', // Changed cache key to avoid corrupted cache
          stalePeriod: const Duration(days: 7),
          maxNrOfCacheObjects: 2000,
          repo: JsonCacheInfoRepository(databaseName: 'RoomEight_V2'),
          fileService: HttpFileService(),
        ),
      );
    } catch (e) {
      // If cache creation fails, clear the cache and try again
      _clearCorruptedCache();
      _cacheManager = CacheManager(
        Config(
          'RoomEight_V3', // Use different cache key as fallback
          stalePeriod: const Duration(days: 7),
          maxNrOfCacheObjects: 2000,
          repo: JsonCacheInfoRepository(databaseName: 'RoomEight_V3'),
          fileService: HttpFileService(),
        ),
      );
    }

    return _cacheManager!;
  }

  /// Clear corrupted cache files
  static Future<void> _clearCorruptedCache() async {
    try {
      // Clear the old corrupted cache
      final oldCacheManager = CacheManager(
        Config(
          'RoomEight',
          repo: JsonCacheInfoRepository(databaseName: 'RoomEight'),
        ),
      );
      await oldCacheManager.emptyCache();
    } catch (e) {
      // Ignore errors when clearing corrupted cache
      Logger.lOG('Error clearing corrupted cache: $e');
    }
  }

  /// Validate and sanitize image URL to prevent invalid image data errors
  static bool _isValidImageUrl(String url) {
    if (url.isEmpty) return false;

    // Check if URL contains common invalid patterns
    if (url.endsWith('null') ||
        url.endsWith('undefined') ||
        url == 'https://room8.flexioninfotech.com') {
      return false;
    }

    // Basic URL validation
    try {
      final uri = Uri.parse(url);
      return uri.isAbsolute && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  Widget _buildWidget() {
    return Padding(
      padding: margin ?? EdgeInsets.zero,
      child: InkWell(onTap: onTap, child: _buildCircleImage()),
    );
  }

  _buildCircleImage() {
    if (radius != null) {
      return ClipRRect(
        borderRadius: radius ?? BorderRadius.zero,
        child: _buildImageWithBorder(),
      );
    } else {
      return _buildImageWithBorder();
    }
  }

  _buildImageWithBorder() {
    if (border != null) {
      return Container(
        decoration: BoxDecoration(border: border, borderRadius: radius),
        child: _buildImageView(),
      );
    } else {
      return _buildImageView();
    }
  }

  Widget _buildImageView() {
    if (imagePath != null) {
      switch (imagePath!.imageType) {
        case ImageType.svg:
          return SizedBox(
            height: height,
            width: width,
            child: SvgPicture.asset(
              imagePath!,
              height: height,
              width: width,
              fit: fit ?? BoxFit.contain,
              colorFilter: color != null
                  ? ColorFilter.mode(color!, BlendMode.srcIn)
                  : null,
            ),
          );
        case ImageType.file:
          return Image.file(
            File(imagePath!),
            height: height,
            width: width,
            fit: fit ?? BoxFit.cover,
            color: color,
          );
        case ImageType.network:
          // Validate URL before attempting to load
          if (!_isValidImageUrl(imagePath!)) {
            return Assets.images.pngs.other.pngAppLogo.image(
              height: height,
              width: width,
              fit: fit ?? BoxFit.cover,
            );
          }

          return CachedNetworkImage(
            height: height,
            width: width,
            fit: fit,
            imageUrl: imagePath!,
            filterQuality: FilterQuality.medium,
            color: color,
            repeat: ImageRepeat.noRepeat,
            placeholderFadeInDuration: fadeInDuration,
            fadeInDuration: fadeInDuration,
            fadeOutDuration: fadeOutDuration,
            colorBlendMode: colorBlendMode ?? BlendMode.srcOver,
            fadeOutCurve: Curves.easeOut,
            placeholder: usePlaceholder
                ? (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey.shade200,
                    highlightColor: Colors.grey.shade50,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(color: Colors.grey.shade200),
                    ),
                  )
                : null,
            cacheManager: getCacheManager(),
            errorWidget: (context, url, error) => Assets
                .images
                .pngs
                .other
                .pngAppLogo
                .image(height: height, width: width, fit: fit ?? BoxFit.cover),
          );
        case ImageType.lottie:
          return Lottie.asset(
            imagePath!,
            height: height,
            width: width,
            fit: fit ?? BoxFit.cover,
          );
        case ImageType.png:
        default:
          return Image.asset(
            imagePath!,
            height: height,
            width: width,
            fit: fit ?? BoxFit.cover,
            color: color,
            filterQuality: FilterQuality.medium,
            gaplessPlayback: true,
            alignment: Alignment.center,
          );
      }
    }
    return const SizedBox.shrink();
  }
}

extension ImageTypeExtension on String {
  ImageType get imageType {
    if (startsWith('http') || startsWith('https')) {
      return ImageType.network;
    } else if (endsWith('.svg')) {
      return ImageType.svg;
    } else if (endsWith('.json')) {
      return ImageType.lottie;
    } else if (startsWith('/data') || startsWith('/storage')) {
      return ImageType.file;
    } else {
      return ImageType.png;
    }
  }
}

enum ImageType { svg, png, network, file, lottie, unknown }
