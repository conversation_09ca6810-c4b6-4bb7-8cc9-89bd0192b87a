part of 'profile_bloc.dart';

class ProfileState extends Equatable {
  final TextEditingController leasePeriodController;
  final List<String> photoPaths;
  final List<String> originalPhotoPaths;
  final List<String> removedPhotoPaths;
  final List<ProfilePicture> originalProfilePictures;
  final String profileImagePath;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final String gender;
  final TextEditingController ageController;
  final List<String> personalityTags;
  final List<String> customPersonalityTags;
  final GlobalKey<FormState> addPersonalDetailFormKey;
  final TextEditingController? fullNameController;
  final FocusNode? fullNameFocusNode;
  final TextEditingController? dobController;
  final TextEditingController? searchController;
  final FocusNode? dobFocusNode;
  final bool isEditProfileLoading;
  final File? userProfile;
  final String? selectedGender;
  final String? selectedPreferredGender;
  final String? selectedPeriod;
  final String? selectedSmokingPerson;
  final String? sleectedCleanLevenl;
  final String? selectedPet;
  final String? selectedClassStand;
  final List<int> selectedHabitsAndLifestyle;
  final List<int> selectedCleanlinessLivingStyle;
  final List<int> selectedInterestsHobbies;
  final bool isPickThings;
  final int selectedOption;
  final TextEditingController contactNumberController;
  final FocusNode contactNumberFocusNode;
  final TextEditingController preferredLocationsController;
  final List<LocationModel> selectedLocations;
  final TextEditingController aboutController;
  final FocusNode aboutFocusNode;
  final bool profileLoading;
  final bool isGetUserProfileLoading;
  final List<ProfileOptionModel> habitsLifestyle;
  final List<ProfileOptionModel> livingStyle;
  final List<ProfileOptionModel> interestsHobbies;

  final TextEditingController otpController;
  final bool isLogout;

  // Backup state fields for restoring when canceling edits
  final String? backupNameText;
  final String? backupSelectedGender;
  final String? backupSelectedPreferredGender;
  final String? backupSelectedSmokingPerson;
  final String? backupSleectedCleanLevenl;
  final String? backupSelectedPet;
  final String? backupSelectedClassStand;
  final String? backupSelectedPeriod;
  final String? backupContactNumberText;
  final String? backupAboutText;
  final String? backupDobText;
  final String? backupAgeText;
  final List<int>? backupSelectedHabitsAndLifestyle;
  final List<int>? backupSelectedCleanlinessLivingStyle;
  final List<int>? backupSelectedInterestsHobbies;
  final List<LocationModel>? backupSelectedLocations;
  final List<String>? backupPersonalityTags;
  final List<String>? backupCustomPersonalityTags;
  final List<String>? backupPhotoPaths;
  final String? backupProfileImagePath;
  final File? backupUserProfile;

  const ProfileState({
    required this.leasePeriodController,
    required this.photoPaths,
    required this.originalPhotoPaths,
    required this.removedPhotoPaths,
    required this.originalProfilePictures,
    required this.profileImagePath,
    required this.nameController,
    required this.emailController,
    required this.gender,
    required this.ageController,
    required this.personalityTags,
    required this.customPersonalityTags,
    required this.addPersonalDetailFormKey,
    this.fullNameController,
    this.fullNameFocusNode,
    this.dobController,
    this.searchController,
    this.dobFocusNode,
    this.isEditProfileLoading = false,
    this.userProfile,
    this.selectedGender,
    this.selectedPreferredGender,
    this.selectedPeriod = '9 months',
    this.selectedSmokingPerson,
    this.sleectedCleanLevenl,
    this.selectedPet,
    this.selectedClassStand,
    this.selectedHabitsAndLifestyle = const [],
    this.selectedCleanlinessLivingStyle = const [],
    this.selectedInterestsHobbies = const [],
    this.isPickThings = false,
    this.selectedOption = 0,
    required this.contactNumberController,
    required this.contactNumberFocusNode,
    required this.preferredLocationsController,
    this.selectedLocations = const [],
    required this.aboutController,
    required this.aboutFocusNode,

    this.profileLoading = false,
    this.isGetUserProfileLoading = false,
    this.habitsLifestyle = const [],
    this.livingStyle = const [],
    this.interestsHobbies = const [],
    required this.otpController,
    this.isLogout = false,

    // Backup state fields
    this.backupNameText,
    this.backupSelectedGender,
    this.backupSelectedPreferredGender,
    this.backupSelectedSmokingPerson,
    this.backupSleectedCleanLevenl,
    this.backupSelectedPet,
    this.backupSelectedClassStand,
    this.backupSelectedPeriod,
    this.backupContactNumberText,
    this.backupAboutText,
    this.backupDobText,
    this.backupAgeText,
    this.backupSelectedHabitsAndLifestyle,
    this.backupSelectedCleanlinessLivingStyle,
    this.backupSelectedInterestsHobbies,
    this.backupSelectedLocations,
    this.backupPersonalityTags,
    this.backupCustomPersonalityTags,
    this.backupPhotoPaths,
    this.backupProfileImagePath,
    this.backupUserProfile,
  });

  int get totalSelectedOptions =>
      selectedHabitsAndLifestyle.length +
      selectedCleanlinessLivingStyle.length +
      selectedInterestsHobbies.length;

  factory ProfileState.initial() {
    final predefinedTags = [];

    return ProfileState(
      leasePeriodController: TextEditingController(text: ''),
      photoPaths: [],
      originalPhotoPaths: [],
      removedPhotoPaths: [],
      originalProfilePictures: [],
      profileImagePath: '',
      nameController: TextEditingController(text: ''),
      emailController: TextEditingController(text: ''),
      gender: '',
      ageController: TextEditingController(text: ''),
      personalityTags: List<String>.from(predefinedTags),
      customPersonalityTags: const [],
      addPersonalDetailFormKey: GlobalKey<FormState>(),
      fullNameController: TextEditingController(),
      fullNameFocusNode: FocusNode(),
      dobController: TextEditingController(),
      dobFocusNode: FocusNode(),
      contactNumberController: TextEditingController(),
      contactNumberFocusNode: FocusNode(),
      preferredLocationsController: TextEditingController(),
      aboutController: TextEditingController(),
      aboutFocusNode: FocusNode(),
      otpController: TextEditingController(),
      isLogout: false,
    );
  }

  ProfileState copyWith({
    String? phone,
    TextEditingController? phoneController,
    FocusNode? phoneFocusNode,
    String? leasePeriod,
    TextEditingController? leasePeriodController,
    List<String>? photoPaths,
    List<String>? originalPhotoPaths,
    List<String>? removedPhotoPaths,
    List<ProfilePicture>? originalProfilePictures,
    String? profileImagePath,
    TextEditingController? nameController,
    TextEditingController? emailController,
    String? gender,
    TextEditingController? ageController,
    List<String>? personalityTags,
    List<String>? customPersonalityTags,
    GlobalKey<FormState>? addPersonalDetailFormKey,
    TextEditingController? fullNameController,
    FocusNode? fullNameFocusNode,
    TextEditingController? dobController,
    FocusNode? dobFocusNode,
    final TextEditingController? searchController,
    bool? isEditProfileLoading,
    File? userProfile,
    final String? selectedGender,
    final String? selectedPreferredGender,
    final String? selectedPeriod,
    final String? selectedSmokingPerson,
    final String? sleectedCleanLevenl,
    final String? selectedPet,
    final String? selectedClassStand,
    final List<int>? selectedHabitsAndLifestyle,
    final List<int>? selectedCleanlinessLivingStyle,
    final List<int>? selectedInterestsHobbies,
    final bool? isPickThings,
    final int? selectedOption,
    TextEditingController? contactNumberController,
    FocusNode? contactNumberFocusNode,
    TextEditingController? preferredLocationsController,
    List<LocationModel>? selectedLocations,
    TextEditingController? aboutController,
    FocusNode? aboutFocusNode,

    final bool? profileLoading,
    final bool? isGetUserProfileLoading,
    final List<ProfileOptionModel>? habitsLifestyle,
    final List<ProfileOptionModel>? livingStyle,
    final List<ProfileOptionModel>? interestsHobbies,
    TextEditingController? otpController,
    bool? isLogout,

    // Backup state fields
    String? backupNameText,
    String? backupSelectedGender,
    String? backupSelectedPreferredGender,
    String? backupSelectedSmokingPerson,
    String? backupSleectedCleanLevenl,
    String? backupSelectedPet,
    String? backupSelectedClassStand,
    String? backupSelectedPeriod,
    String? backupContactNumberText,
    String? backupAboutText,
    String? backupDobText,
    String? backupAgeText,
    List<int>? backupSelectedHabitsAndLifestyle,
    List<int>? backupSelectedCleanlinessLivingStyle,
    List<int>? backupSelectedInterestsHobbies,
    List<LocationModel>? backupSelectedLocations,
    List<String>? backupPersonalityTags,
    List<String>? backupCustomPersonalityTags,
    List<String>? backupPhotoPaths,
    String? backupProfileImagePath,
    File? backupUserProfile,
  }) {
    return ProfileState(
      leasePeriodController:
          leasePeriodController ?? this.leasePeriodController,
      photoPaths: photoPaths ?? this.photoPaths,
      originalPhotoPaths: originalPhotoPaths ?? this.originalPhotoPaths,
      removedPhotoPaths: removedPhotoPaths ?? this.removedPhotoPaths,
      originalProfilePictures:
          originalProfilePictures ?? this.originalProfilePictures,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      nameController: nameController ?? this.nameController,
      emailController: emailController ?? this.emailController,
      gender: gender ?? this.gender,
      ageController: ageController ?? this.ageController,
      personalityTags: personalityTags ?? this.personalityTags,
      customPersonalityTags:
          customPersonalityTags ?? this.customPersonalityTags,
      addPersonalDetailFormKey:
          addPersonalDetailFormKey ?? this.addPersonalDetailFormKey,
      fullNameController: fullNameController ?? this.fullNameController,
      fullNameFocusNode: fullNameFocusNode ?? this.fullNameFocusNode,
      dobController: dobController ?? this.dobController,
      dobFocusNode: dobFocusNode ?? this.dobFocusNode,
      isEditProfileLoading: isEditProfileLoading ?? this.isEditProfileLoading,
      userProfile: userProfile ?? this.userProfile,
      selectedGender: selectedGender ?? this.selectedGender,
      selectedPreferredGender:
          selectedPreferredGender ?? this.selectedPreferredGender,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
      selectedSmokingPerson:
          selectedSmokingPerson ?? this.selectedSmokingPerson,
      sleectedCleanLevenl: sleectedCleanLevenl ?? this.sleectedCleanLevenl,
      selectedPet: selectedPet ?? this.selectedPet,
      selectedClassStand: selectedClassStand ?? this.selectedClassStand,
      selectedHabitsAndLifestyle:
          selectedHabitsAndLifestyle ?? this.selectedHabitsAndLifestyle,
      selectedCleanlinessLivingStyle:
          selectedCleanlinessLivingStyle ?? this.selectedCleanlinessLivingStyle,
      selectedInterestsHobbies:
          selectedInterestsHobbies ?? this.selectedInterestsHobbies,
      isPickThings: isPickThings ?? this.isPickThings,
      searchController: searchController ?? this.searchController,
      selectedOption: selectedOption ?? this.selectedOption,
      contactNumberController:
          contactNumberController ?? this.contactNumberController,
      contactNumberFocusNode:
          contactNumberFocusNode ?? this.contactNumberFocusNode,
      preferredLocationsController:
          preferredLocationsController ?? this.preferredLocationsController,
      selectedLocations: selectedLocations ?? this.selectedLocations,
      aboutController: aboutController ?? this.aboutController,
      aboutFocusNode: aboutFocusNode ?? this.aboutFocusNode,

      profileLoading: profileLoading ?? this.profileLoading,
      isGetUserProfileLoading:
          isGetUserProfileLoading ?? this.isGetUserProfileLoading,
      habitsLifestyle: habitsLifestyle ?? this.habitsLifestyle,
      livingStyle: livingStyle ?? this.livingStyle,
      interestsHobbies: interestsHobbies ?? this.interestsHobbies,
      otpController: otpController ?? this.otpController,
      isLogout: isLogout ?? this.isLogout,

      // Backup state fields
      backupNameText: backupNameText ?? this.backupNameText,
      backupSelectedGender: backupSelectedGender ?? this.backupSelectedGender,
      backupSelectedPreferredGender:
          backupSelectedPreferredGender ?? this.backupSelectedPreferredGender,
      backupSelectedSmokingPerson:
          backupSelectedSmokingPerson ?? this.backupSelectedSmokingPerson,
      backupSleectedCleanLevenl:
          backupSleectedCleanLevenl ?? this.backupSleectedCleanLevenl,
      backupSelectedPet: backupSelectedPet ?? this.backupSelectedPet,
      backupSelectedClassStand:
          backupSelectedClassStand ?? this.backupSelectedClassStand,
      backupSelectedPeriod: backupSelectedPeriod ?? this.backupSelectedPeriod,
      backupContactNumberText:
          backupContactNumberText ?? this.backupContactNumberText,
      backupAboutText: backupAboutText ?? this.backupAboutText,
      backupDobText: backupDobText ?? this.backupDobText,
      backupAgeText: backupAgeText ?? this.backupAgeText,
      backupSelectedHabitsAndLifestyle:
          backupSelectedHabitsAndLifestyle ??
          this.backupSelectedHabitsAndLifestyle,
      backupSelectedCleanlinessLivingStyle:
          backupSelectedCleanlinessLivingStyle ??
          this.backupSelectedCleanlinessLivingStyle,
      backupSelectedInterestsHobbies:
          backupSelectedInterestsHobbies ?? this.backupSelectedInterestsHobbies,
      backupSelectedLocations:
          backupSelectedLocations ?? this.backupSelectedLocations,
      backupPersonalityTags:
          backupPersonalityTags ?? this.backupPersonalityTags,
      backupCustomPersonalityTags:
          backupCustomPersonalityTags ?? this.backupCustomPersonalityTags,
      backupPhotoPaths: backupPhotoPaths ?? this.backupPhotoPaths,
      backupProfileImagePath:
          backupProfileImagePath ?? this.backupProfileImagePath,
      backupUserProfile: backupUserProfile ?? this.backupUserProfile,
    );
  }

  @override
  List<Object?> get props => [
    leasePeriodController,
    photoPaths,
    originalPhotoPaths,
    removedPhotoPaths,
    originalProfilePictures,
    profileImagePath,
    nameController,
    emailController,
    gender,
    ageController,
    personalityTags,
    customPersonalityTags,
    addPersonalDetailFormKey,
    fullNameController,
    fullNameFocusNode,
    dobController,
    dobFocusNode,
    searchController,
    isEditProfileLoading,
    userProfile,
    selectedGender,
    selectedPreferredGender,
    selectedPeriod,
    selectedSmokingPerson,
    sleectedCleanLevenl,
    selectedPet,
    selectedClassStand,
    selectedHabitsAndLifestyle,
    selectedCleanlinessLivingStyle,
    selectedInterestsHobbies,
    isPickThings,
    selectedOption,
    contactNumberController,
    contactNumberFocusNode,
    preferredLocationsController,
    selectedLocations,
    aboutController,
    isGetUserProfileLoading,
    aboutFocusNode,
    habitsLifestyle,
    livingStyle,
    interestsHobbies,
    otpController,
    isLogout,

    // Backup state fields
    backupNameText,
    backupSelectedGender,
    backupSelectedPreferredGender,
    backupSelectedSmokingPerson,
    backupSleectedCleanLevenl,
    backupSelectedPet,
    backupSelectedClassStand,
    backupSelectedPeriod,
    backupContactNumberText,
    backupAboutText,
    backupDobText,
    backupAgeText,
    backupSelectedHabitsAndLifestyle,
    backupSelectedCleanlinessLivingStyle,
    backupSelectedInterestsHobbies,
    backupSelectedLocations,
    backupPersonalityTags,
    backupCustomPersonalityTags,
    backupPhotoPaths,
    backupProfileImagePath,
    backupUserProfile,
  ];
}
