import 'package:flutter/material.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/common_widget/common_shimmer.dart';

class HomeScreenShimmer extends StatelessWidget {
  const HomeScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main card shimmer
        _buildMainCardShimmer(context),

        // Top section shimmer
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: _buildTopSectionShimmer(context),
        ),
      ],
    );
  }

  Widget _buildMainCardShimmer(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: CommonShimmer(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(0),
          ),
          child: Stack(
            children: [
              // Background image shimmer
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(color: Colors.grey[400]),
              ),

              // Gradient overlay
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.3),
                    ],
                  ),
                ),
              ),

              // Bottom user info shimmer
              Positioned(
                bottom: 90.0,
                left: 16.0,
                right: 16.0,
                child: _buildUserInfoShimmer(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopSectionShimmer(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.3), Colors.transparent],
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          top: 60.0,
          bottom: 16.0,
        ),
        child: CommonShimmer(
          baseColor: Colors.white.withValues(alpha: 0.3),
          highlightColor: Colors.white.withValues(alpha: 0.1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left button shimmer
              _buildTopBarButtonShimmer(),
              // Right button shimmer
              _buildTopBarButtonShimmer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBarButtonShimmer() {
    return Container(
      height: 36.0,
      width: 36.0,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(18.0),
      ),
    );
  }

  Widget _buildUserInfoShimmer(BuildContext context) {
    return CommonShimmer(
      baseColor: Colors.white.withValues(alpha: 0.3),
      highlightColor: Colors.white.withValues(alpha: 0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User profile row shimmer
          Row(
            children: [
              // Profile image shimmer
              Container(
                height: 50.0,
                width: 50.0,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
              SizedBox(width: 12.0),
              // Name shimmer
              Container(
                height: 18.0,
                width: 120.0,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(9.0),
                ),
              ),
            ],
          ),

          SizedBox(height: 12.0),

          // Chips shimmer
          _buildChipsShimmer(),

          SizedBox(height: 12.0),

          // About text shimmer
          _buildAboutTextShimmer(context),
        ],
      ),
    );
  }

  Widget _buildChipsShimmer() {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: [
        _buildChipShimmer(80.0),
        _buildChipShimmer(60.0),
        _buildChipShimmer(100.0),
        _buildChipShimmer(70.0),
        _buildChipShimmer(90.0),
      ],
    );
  }

  Widget _buildChipShimmer(double width) {
    return Container(
      width: width,
      height: 28.0,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(14.0),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
    );
  }

  Widget _buildAboutTextShimmer(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 14.0,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.4),
            borderRadius: BorderRadius.circular(7.0),
          ),
        ),
        SizedBox(height: 6.0),
        Container(
          height: 14.0,
          width: MediaQuery.of(context).size.width * 0.7,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.4),
            borderRadius: BorderRadius.circular(7.0),
          ),
        ),
        SizedBox(height: 6.0),
        Container(
          height: 14.0,
          width: MediaQuery.of(context).size.width * 0.5,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.4),
            borderRadius: BorderRadius.circular(7.0),
          ),
        ),
      ],
    );
  }
}

// Alternative animated shimmer version with pulse effect
class AnimatedHomeScreenShimmer extends StatefulWidget {
  const AnimatedHomeScreenShimmer({super.key});

  @override
  State<AnimatedHomeScreenShimmer> createState() =>
      _AnimatedHomeScreenShimmerState();
}

class _AnimatedHomeScreenShimmerState extends State<AnimatedHomeScreenShimmer>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for shimmer effect
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(begin: 0.3, end: 0.7).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Slide animation for card effect
    _slideController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _slideAnimation =
        Tween<Offset>(
          begin: const Offset(-1.0, 0.0),
          end: const Offset(1.0, 0.0),
        ).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeInOut),
        );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main card shimmer with animation
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300]?.withValues(
                  alpha: _pulseAnimation.value,
                ),
              ),
              child: Stack(
                children: [
                  // Animated shimmer overlay
                  AnimatedBuilder(
                    animation: _slideAnimation,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _slideAnimation,
                        child: Container(
                          width: 100,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.transparent,
                                Colors.white.withValues(alpha: 0.4),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // Bottom user info
                  Positioned(
                    bottom: 90.0,
                    left: 16.0,
                    right: 16.0,
                    child: _buildAnimatedUserInfo(),
                  ),
                ],
              ),
            );
          },
        ),

        // Top section
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: _buildAnimatedTopSection(),
        ),
      ],
    );
  }

  Widget _buildAnimatedTopSection() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.3), Colors.transparent],
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          top: 60.0,
          bottom: 16.0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Container(
                  height: 36.0,
                  width: 36.0,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(
                      alpha: _pulseAnimation.value * 0.5,
                    ),
                    borderRadius: BorderRadius.circular(18.0),
                  ),
                );
              },
            ),
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Container(
                  height: 36.0,
                  width: 36.0,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(
                      alpha: _pulseAnimation.value * 0.5,
                    ),
                    borderRadius: BorderRadius.circular(18.0),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedUserInfo() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile row
            Row(
              children: [
                Container(
                  height: 50.0,
                  width: 50.0,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(
                      alpha: _pulseAnimation.value * 0.6,
                    ),
                    borderRadius: BorderRadius.circular(25.0),
                  ),
                ),
                SizedBox(width: 12.0),
                Container(
                  height: 18.0,
                  width: 120.0,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(
                      alpha: _pulseAnimation.value * 0.6,
                    ),
                    borderRadius: BorderRadius.circular(9.0),
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.0),

            // Animated chips
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: [
                _buildAnimatedChip(80.0, 0),
                _buildAnimatedChip(60.0, 1),
                _buildAnimatedChip(100.0, 2),
                _buildAnimatedChip(70.0, 3),
              ],
            ),

            SizedBox(height: 12.0),

            // About text lines
            ..._buildAnimatedTextLines(),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedChip(double width, int index) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        // Stagger the animation for each chip
        final staggeredValue = (_pulseAnimation.value + (index * 0.1)) % 1.0;
        return Container(
          width: width,
          height: 28.0,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: staggeredValue * 0.5),
            borderRadius: BorderRadius.circular(14.0),
            border: Border.all(
              color: Colors.white.withValues(alpha: staggeredValue * 0.3),
              width: 1,
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildAnimatedTextLines() {
    return [
      Container(
        height: 14.0,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.6),
          borderRadius: BorderRadius.circular(7.0),
        ),
      ),
      SizedBox(height: 6.0),
      Container(
        height: 14.0,
        width: MediaQuery.of(context).size.width * 0.7,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.6),
          borderRadius: BorderRadius.circular(7.0),
        ),
      ),
      SizedBox(height: 6.0),
      Container(
        height: 14.0,
        width: MediaQuery.of(context).size.width * 0.5,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.6),
          borderRadius: BorderRadius.circular(7.0),
        ),
      ),
    ];
  }
}
