import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  static Widget builder(BuildContext context) => const SignupScreen();

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  late final GlobalKey<FormState> _formKey;

  @override
  void initState() {
    super.initState();
    _formKey = GlobalKey<FormState>();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: Stack(
            children: [
              AbsorbPointer(
                absorbing: state.isSignupLoading,
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: BackgroundImage(
                      imagePath: Assets.images.pngs.other.pngSignupBg.path,
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: MediaQuery.of(context).size.height,
                        ),
                        child: IntrinsicHeight(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _buildSignupHeader(context),
                              _buildSignupForm(context, state),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSignupHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 38.w,
        right: 38.w,
        top: 126.h,
        bottom: 32.h,
      ),
      child: Column(
        children: [
          Text(
            Lang.of(context).lbl_signup_message,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.fillColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignupForm(BuildContext context, AuthState state) {
    return Flexible(
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).customColors.fillColor?.withValues(alpha: 0.8),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _builSignupTitle(context),
                  buildSizedBoxH(24.h),
                  _buildSignupNameField(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignupEmailField(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignupPasswordField(context, state),
                  buildSizedBoxH(16.h),
                  _buildsignupConfPasswordField(context, state),
                  buildSizedBoxH(4.h),
                  // _buildForgotPassword(context),
                  buildSizedBoxH(24.h),
                  const Spacer(),
                  _buildSignupButton(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignUpOption(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _builSignupTitle(BuildContext context) {
    return Text(
      Lang.of(context).lbl_sign_up,
      textAlign: TextAlign.center,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 22.sp,
        color: Theme.of(context).customColors.blackColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSignupNameField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_full_name,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          hintLabel: Lang.of(context).lbl_enter_your_full_name,
          controller: state.signupNameController,
          focusNode: state.signupNamefocusnode,
          textInputAction: TextInputAction.next,
          isCapitalized: true,
          validator: (value) => AppValidations.nameValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupNameChanged(value)),
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.0),
            imagePath: Assets.images.svgs.icons.icProfile.path,
          ),
        ),
      ],
    );
  }

  Widget _buildSignupEmailField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_school_id,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.email,
          hintLabel: Lang.of(context).lbl_emptyEmail,
          controller: state.signupEmailController,
          focusNode: state.signupEmailfocusnode,
          textInputAction: TextInputAction.next,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.0),
            imagePath: Assets.images.svgs.icons.icMail.path,
          ),
          validator: (value) => AppValidations.emailValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupEmailChanged(value)),
        ),
      ],
    );
  }

  Widget _buildSignupPasswordField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_password,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.password,
          hintLabel: Lang.of(context).lbl_emptyPassword,
          controller: state.signupPasswordController,
          focusNode: state.signupPasswordfocusnode,
          textInputAction: TextInputAction.next,
          obscureText: state.signupObscurePassword,
          validator: (value) =>
              AppValidations.passwordValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupPasswordChanged(value)),
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.r),
            imagePath: Assets.images.svgs.icons.icLock.path,
          ),

          suffixIcon: CustomImageView(
            margin: EdgeInsets.all(12.0),
            imagePath: state.signupObscurePassword
                ? Assets.images.svgs.icons.icEyeOff.path
                : Assets.images.svgs.icons.icEyeOn.path,
            onTap: () {
              context.read<AuthBloc>().add(ToggleSignupPasswordVisibility());
            },
          ),
        ),
      ],
    );
  }

  Widget _buildsignupConfPasswordField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_conf_password,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.password,
          hintLabel: Lang.of(context).lbl_emptyConfPassword,
          controller: state.signupConfirmPasswordController,
          focusNode: state.signupConfirmPasswordfocusnode,
          textInputAction: TextInputAction.done,
          obscureText: state.signupObscureConfirmPassword,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.r),
            imagePath: Assets.images.svgs.icons.icLock.path,
          ),
          validator: (value) => AppValidations.confPasswordValidation(
            value,
            state.signupPasswordController?.text ?? '',
            context,
          ),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupConfirmPasswordChanged(value)),
          suffixIcon: CustomImageView(
            margin: EdgeInsets.all(12.0),
            imagePath: state.signupObscureConfirmPassword
                ? Assets.images.svgs.icons.icEyeOff.path
                : Assets.images.svgs.icons.icEyeOn.path,
            onTap: () {
              context.read<AuthBloc>().add(
                ToggleSignupConfirmPasswordVisibility(),
              );
            },
          ),
        ),
      ],
    );
  }

  // bool _passwordsMatch(AuthState state) {
  //   final password = state.signupPasswordController?.text ?? '';
  //   final confirmPassword = state.signupConfirmPasswordController?.text ?? '';
  //   return password.isNotEmpty &&
  //       confirmPassword.isNotEmpty &&
  //       password == confirmPassword;
  // }

  Widget _buildSignupButton(BuildContext context, AuthState state) {
    // final passwordsMatch = _passwordsMatch(state);
    // final hasPassword = (state.signupPasswordController?.text ?? '').isNotEmpty;
    // final hasConfirmPassword =
    //     (state.signupConfirmPasswordController?.text ?? '').isNotEmpty;

    return Column(
      children: [
        CustomElevatedButton(
          isLoading: state.isSignupLoading,
          isDisabled: state.isSignupLoading,
          text: Lang.of(context).lbl_sign_up,
          buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).customColors.fillColor,
            fontSize: 18.0.sp,
            fontWeight: FontWeight.w500,
          ),
          onPressed: () {
            FocusManager.instance.primaryFocus?.unfocus();
            if (_formKey.currentState?.validate() ?? false) {
              context.read<AuthBloc>().add(SignupSubmitted());
            }
          },
        ),
      ],
    );
  }

  Widget _buildSignUpOption(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          Lang.of(context).lbl_login_redirect,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).customColors.blackColor,
            fontSize: 14.sp,
          ),
        ),
        TextButton(
          onPressed: () {
            NavigatorService.pushAndRemoveUntil(AppRoutes.loginScreen);
          },
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            Lang.of(context).lbl_login,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).customColors.primaryColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              decoration: TextDecoration.underline,
              decorationColor: Theme.of(context).customColors.primaryColor,
            ),
          ),
        ),
      ],
    );
  }
}
