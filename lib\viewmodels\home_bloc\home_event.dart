part of 'home_bloc.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class GetAllProfile extends HomeEvent {}

class HomePageChanged extends HomeEvent {
  final int index;
  const HomePageChanged(this.index);

  @override
  List<Object> get props => [index];
}

class HomeResetToFirst extends HomeEvent {
  const HomeResetToFirst();
}

class AcceptUser extends HomeEvent {
  // final UserData user;
  final int userId;
  // const AcceptUser(this.user);
  const AcceptUser(this.userId);

  @override
  // List<Object> get props => [user];
  List<Object> get props => [userId];
}

class RejectUser extends HomeEvent {
  // final UserData user;
  final int userId;
  // const AcceptUser(this.user);
  const RejectUser(this.userId);

  @override
  // List<Object> get props => [user];
  List<Object> get props => [userId];
}

class StartPulseAnimation extends HomeEvent {}

class StopPulseAnimation extends HomeEvent {}

class CarouselPageChanged extends HomeEvent {
  final int index;
  const CarouselPageChanged(this.index);

  @override
  List<Object> get props => [index];
}

class CheckTutorialStatus extends HomeEvent {
  const CheckTutorialStatus();
}

class DismissTutorial extends HomeEvent {
  const DismissTutorial();
}

class InitializeSwipableController extends HomeEvent {
  const InitializeSwipableController();
}

class DisposeSwipableController extends HomeEvent {
  const DisposeSwipableController();
}

class GetUserProfileByID extends HomeEvent {
  final int userId;
  const GetUserProfileByID(this.userId);

  @override
  // List<Object> get props => [user];
  List<Object> get props => [userId];
}

class ReportChange extends HomeEvent {
  final String report;
  const ReportChange(this.report);
}

class BlockProfileSubmit extends HomeEvent {
  final int userId;
  const BlockProfileSubmit(this.userId);
}

class ReportProfileSubmit extends HomeEvent {
  final int userId;
  const ReportProfileSubmit(this.userId);
}

class SwipeInteractionChanged extends HomeEvent {
  final bool isInteracting;
  const SwipeInteractionChanged(this.isInteracting);
}

class CurrentProfileIdChange extends HomeEvent {
  final int index;

  const CurrentProfileIdChange(this.index);
}

class AllCardsSwiped extends HomeEvent {
  const AllCardsSwiped();
}
