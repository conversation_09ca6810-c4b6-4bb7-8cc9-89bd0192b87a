import 'dart:convert';

class UserDataModel {
  final bool? status;
  final String? message;
  final List<UserData>? users;

  UserDataModel({this.status, this.message, this.users});

  factory UserDataModel.fromJson(Map<String, dynamic> json) {
    return UserDataModel(
      status: json['status'],
      message: json['message'],
      users: (json['data'] as List<dynamic>?)
          ?.map((item) => UserData.fromJson(item))
          .toList(),
    );
  }
}

class UserData {
  final int? id;
  final String? name;
  final String? profileImage;
  final String? preferedSmoking;
  final String? cleaniness;
  final String? preferedLeasePeriod;
  final bool? pets;
  final String? about;
  final String? personalityTypeDescription;
  final List<int>? habitsLifestyle;
  final List<int>? livingStyle;
  final List<int>? interestsHobbies;
  final String? classStanding;

  UserData({
    this.id,
    this.name,
    this.profileImage,
    this.preferedSmoking,
    this.cleaniness,
    this.preferedLeasePeriod,
    this.pets,
    this.about,
    this.personalityTypeDescription,
    this.habitsLifestyle,
    this.livingStyle,
    this.interestsHobbies,
    this.classStanding,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    List<int>? parseStringToIntList(String? value) {
      try {
        if (value == null || value.isEmpty) return [];
        List<dynamic> decoded = jsonDecode(value);
        return decoded.whereType<int>().toList();
      } catch (_) {
        return [];
      }
    }

    return UserData(
      id: json['id'],
      name: json['name'],
      profileImage: json['profile_image'],
      preferedSmoking: json['prefered_smoking'],
      cleaniness: json['cleaniness'],
      preferedLeasePeriod: json['prefered_lease_period'],
      pets: json['pets'],
      about: json['about'],
      personalityTypeDescription: json['personality_type_description'],
      habitsLifestyle: parseStringToIntList(json['habits_lifestyle']),
      livingStyle: parseStringToIntList(json['living_style']),
      interestsHobbies: parseStringToIntList(json['interests_hobbies']),
      classStanding: json['class_standing'],
    );
  }
}
