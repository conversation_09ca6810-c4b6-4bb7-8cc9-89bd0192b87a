import 'package:room_eight/core/utils/app_exports.dart';

class SetNewPasswordScreen extends StatelessWidget {
  static Widget builder(BuildContext context) => const SetNewPasswordScreen();
  const SetNewPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  buildSizedBoxH(175.h),
                  _buildHeaderMessage(context),
                  buildSizedBoxH(50.h),
                  _buildForgotPasswordForm(context, state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

Widget _buildHeaderMessage(BuildContext context) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Column(
        children: [
          Text(
            "Reset Password",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 30.sp,
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          buildSizedBoxH(15.h),
          Text(
            "Enter your new password.",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
        ],
      ),
    ],
  );
}

Widget _buildForgotPasswordForm(BuildContext context, AuthState state) {
  return Form(
    key: state.setNewPasswordFormKey,
    child: Column(
      children: [
        _buildSignupPasswordField(context, state),
        buildSizedBoxH(20.h),
        _buildsignupConfPasswordField(context, state),
        buildSizedBoxH(50.h),
        _buildSignInButton(context, state),
      ],
    ),
  );
}

Widget _buildSignupPasswordField(BuildContext context, AuthState state) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        Lang.of(context).lbl_password,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontSize: 16.sp,
          color: Theme.of(context).customColors.blackColor,
        ),
      ),
      CustomTextInputField(
        context: context,
        type: InputType.password,
        hintLabel: Lang.of(context).lbl_emptyPassword,
        controller: state.newPasswordController,

        textInputAction: TextInputAction.done,
        obscureText: state.signupObscurePassword,
        validator: (value) => AppValidations.passwordValidation(value, context),
        onChanged: (value) =>
            context.read<AuthBloc>().add(NewPasswordChanged(value)),
        prefixIcon: CustomImageView(
          margin: EdgeInsets.all(14.r),
          imagePath: Assets.images.svgs.icons.icLock.path,
        ),

        suffixIcon: IconButton(
          icon: Icon(
            state.signupObscurePassword
                ? Icons.visibility_off
                : Icons.visibility,
          ),
          onPressed: () {
            context.read<AuthBloc>().add(ToggleSignupPasswordVisibility());
          },
        ),
      ),
    ],
  );
}

Widget _buildsignupConfPasswordField(BuildContext context, AuthState state) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        Lang.of(context).lbl_conf_password,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontSize: 16.sp,
          color: Theme.of(context).customColors.blackColor,
        ),
      ),
      CustomTextInputField(
        context: context,
        type: InputType.password,
        hintLabel: Lang.of(context).lbl_emptyConfPassword,
        controller: state.newConfirmPasswordController,
        textInputAction: TextInputAction.done,
        obscureText: state.signupObscureConfirmPassword,
        prefixIcon: CustomImageView(
          margin: EdgeInsets.all(14.r),
          imagePath: Assets.images.svgs.icons.icLock.path,
        ),
        validator: (value) => AppValidations.confPasswordValidation(
          value,
          state.newPasswordController?.text ?? '',
          context,
        ),
        onChanged: (value) =>
            context.read<AuthBloc>().add(NewConfirmPasswordChanged(value)),

        suffixIcon: IconButton(
          icon: Icon(
            state.signupObscureConfirmPassword
                ? Icons.visibility_off
                : Icons.visibility,
          ),
          onPressed: () {
            context.read<AuthBloc>().add(
              ToggleSignupConfirmPasswordVisibility(),
            );
          },
        ),
      ),
    ],
  );
}

Widget _buildSignInButton(BuildContext context, AuthState state) {
  return CustomElevatedButton(
    isLoading: state.isSetNewPassword,
    isDisabled: state.isSetNewPassword,
    text: "Reset",
    buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
      color: Theme.of(context).customColors.fillColor,
      fontSize: 18.0.sp,
      fontWeight: FontWeight.w500,
    ),
    onPressed: () {
      if (state.setNewPasswordFormKey.currentState?.validate() ?? false) {
        context.read<AuthBloc>().add(SetNewPassword());
      }
    },
  );
}
