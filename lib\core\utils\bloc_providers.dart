import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/repository/chat_repository/chat_repository.dart';
import 'package:room_eight/repository/home_repository/home_repository.dart';
import 'package:room_eight/repository/like_repository/like_repository.dart';
import 'package:room_eight/repository/profile_repository/profile_repository.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/viewmodels/onboarding_bloc/onboarding_bloc.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/viewmodels/home_bloc/home_bloc.dart';
import 'package:room_eight/viewmodels/chat_bloc/chat_bloc.dart';

class BlocProviders extends StatelessWidget {
  final Widget child;

  const BlocProviders({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<LocaleBloc>(
          create: (context) =>
              LocaleBloc()..add(SetLocale(locale: Locale('en'))),
        ),
        BlocProvider<ThemeBloc>(
          create: (context) => ThemeBloc()
            ..add(
              InitializeTheme(isDarkThemeOn: false, followSystemTheme: true),
            ),
        ),
        BlocProvider(
          create: (_) => CheckConnectionCubit()..initializeConnectivity(),
        ),

        BlocProvider<SplashBloc>(
          create: (context) =>
              SplashBloc(AuthRepository(apiClient: ApiClient()))
                ..add(SalesAppAuthIntilizeEvent()),
        ),
        BlocProvider<OnboardingBloc>(create: (context) => OnboardingBloc()),
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(AuthRepository(apiClient: ApiClient())),
        ),

        BlocProvider<NavBloc>(create: (context) => NavBloc()),
        BlocProvider<HomeBloc>(
          create: (context) => HomeBloc(
            HomeRepository(apiClient: ApiClient()),
            ProfileRepository(apiClient: ApiClient()),
          ),
        ),
        BlocProvider<ProfileBloc>(
          create: (context) =>
              ProfileBloc(ProfileRepository(apiClient: ApiClient())),
        ),
        BlocProvider<LikeBloc>(
          create: (context) => LikeBloc(LikeRepository(apiClient: ApiClient())),
        ),
        BlocProvider<ChatBloc>(
          create: (context) => ChatBloc(ChatRepository(apiClient: ApiClient())),
        ),
      ],
      child: child,
    );
  }
}
